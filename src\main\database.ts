import { PrismaClient } from '@prisma/client'
import { app } from 'electron'
import path from 'path'
import fs from 'fs'
import type { DatabaseConfig, DatabaseResult } from '../shared/types'
import documentLinkService from './documentLinkService'
// {{ AURA-X: Add - 导入迁移助手. Approval: 寸止(ID:1738157400). }}
import MigrationHelper from './migrationHelper'

class DatabaseService {
  private prisma: PrismaClient | null = null
  private config: DatabaseConfig | null = null
  // {{ AURA-X: Add - 迁移助手实例. Approval: 寸止(ID:1738157400). }}
  private migrationHelper: MigrationHelper | null = null

  /**
   * Initialize database service with user data directory
   */
  async initialize(): Promise<DatabaseResult<DatabaseConfig>> {
    try {
      // Check if we're in development mode
      const isDevelopment = process.env.NODE_ENV === 'development' || !app.isPackaged

      let databasePath: string
      let userDataPath: string

      if (isDevelopment) {
        // In development, use the project's dev.db file
        userDataPath = process.cwd()
        databasePath = path.join(userDataPath, 'prisma', 'dev.db')
        console.log('Development mode: using project database')
      } else {
        // In production, use user data directory
        userDataPath = app.getPath('userData')
        databasePath = path.join(userDataPath, 'paolife.db')
        console.log('Production mode: using user data directory')
      }

      this.config = {
        databasePath,
        userDataPath
      }

      // Ensure directory exists
      const dbDir = path.dirname(databasePath)
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true })
      }

      // Update DATABASE_URL to use the correct path (only in production)
      if (!isDevelopment) {
        process.env.DATABASE_URL = `file:${databasePath}`
      }

      // Initialize Prisma client
      this.prisma = new PrismaClient({
        datasources: {
          db: {
            url: process.env.DATABASE_URL
          }
        }
      })

      // Test connection
      await this.prisma.$connect()

      // {{ AURA-X: Add - 初始化迁移助手并检查迁移. Approval: 寸止(ID:1738157400). }}
      this.migrationHelper = new MigrationHelper(this.prisma)

      // 检查并执行必要的迁移
      try {
        // 在开发环境中，强制检查所有迁移
        const isDevelopment = process.env.NODE_ENV === 'development' || !app.isPackaged
        const migrationResult = isDevelopment
          ? await this.migrationHelper.forceCheckAndExecuteMigrations()
          : await this.migrationHelper.checkAndExecuteMigrations()

        if (migrationResult.migrations.length > 0) {
          console.log('已执行迁移:', migrationResult.migrations)
        }
        if (migrationResult.errors.length > 0) {
          console.warn('迁移错误:', migrationResult.errors)
        }
      } catch (migrationError) {
        console.warn('迁移检查失败，但不影响基本功能:', migrationError)
      }

      // Initialize document link service
      documentLinkService.setPrismaClient(this.prisma)

      console.log(`Database initialized at: ${databasePath}`)

      return {
        success: true,
        data: this.config
      }
    } catch (error) {
      console.error('Failed to initialize database:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get Prisma client instance
   */
  getClient(): PrismaClient {
    if (!this.prisma) {
      throw new Error('Database not initialized. Call initialize() first.')
    }
    return this.prisma
  }

  /**
   * Get database configuration
   */
  getConfig(): DatabaseConfig {
    if (!this.config) {
      throw new Error('Database not initialized. Call initialize() first.')
    }
    return this.config
  }

  /**
   * Get document link service instance
   */
  getDocumentLinkService() {
    return documentLinkService
  }

  /**
   * Close database connection
   */
  async close(): Promise<void> {
    if (this.prisma) {
      await this.prisma.$disconnect()
      this.prisma = null
    }
  }

  /**
   * Test database connection
   */
  async testConnection(): Promise<DatabaseResult<boolean>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.$queryRaw`SELECT 1`

      return {
        success: true,
        data: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Connection test failed'
      }
    }
  }

  /**
   * 获取数据库schema信息（用于调试）
   */
  async getDatabaseInfo(): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma || !this.migrationHelper) {
        throw new Error('Database not initialized')
      }

      const info = await this.migrationHelper.getDatabaseInfo()
      return {
        success: true,
        data: info
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get database info'
      }
    }
  }

  /**
   * Basic CRUD operations for testing
   */
  async createProject(data: {
    name: string
    description?: string
    goal?: string
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const project = await this.prisma.project.create({
        data: {
          name: data.name,
          description: data.description,
          goal: data.goal
        }
      })

      return {
        success: true,
        data: project
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create project'
      }
    }
  }

  async getProjects(): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const projects = await this.prisma.project.findMany({
        where: {
          archived: false
        },
        orderBy: {
          createdAt: 'desc'
        }
      })

      return {
        success: true,
        data: projects
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get projects'
      }
    }
  }

  async getArchivedProjects(): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const projects = await this.prisma.project.findMany({
        where: {
          archived: true
        },
        orderBy: {
          updatedAt: 'desc'
        }
      })

      return {
        success: true,
        data: projects
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get archived projects'
      }
    }
  }

  async getArchivedAreas(): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const areas = await this.prisma.area.findMany({
        where: {
          archived: true
        },
        orderBy: {
          updatedAt: 'desc'
        }
      })

      return {
        success: true,
        data: areas
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get archived areas'
      }
    }
  }

  async archiveProject(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.project.update({
        where: { id },
        data: { archived: true }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to archive project'
      }
    }
  }

  async restoreProject(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.project.update({
        where: { id },
        data: { archived: false }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to restore project'
      }
    }
  }

  async archiveArea(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.area.update({
        where: { id },
        data: { archived: true }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to archive area'
      }
    }
  }

  async restoreArea(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.area.update({
        where: { id },
        data: { archived: false }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to restore area'
      }
    }
  }

  async deleteArea(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.area.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete area'
      }
    }
  }

  /**
   * ProjectKPI operations
   */
  async createProjectKPI(data: {
    projectId: string
    name: string
    value: string
    target?: string
    unit?: string
    frequency?: string
    // {{ AURA-X: Add - 添加direction字段支持. Approval: 寸止(ID:1738157400). }}
    direction?: string
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const kpi = await this.prisma.projectKPI.create({
        data: {
          projectId: data.projectId,
          name: data.name,
          value: data.value,
          target: data.target,
          unit: data.unit,
          frequency: data.frequency,
          // {{ AURA-X: Add - 包含direction字段. Approval: 寸止(ID:1738157400). }}
          direction: data.direction || 'increase'
        }
      })

      return {
        success: true,
        data: kpi
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create project KPI'
      }
    }
  }

  async getProjectKPIs(projectId: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const kpis = await this.prisma.projectKPI.findMany({
        where: {
          projectId: projectId
        },
        orderBy: {
          updatedAt: 'desc'
        }
      })

      return {
        success: true,
        data: kpis
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get project KPIs'
      }
    }
  }

  async updateProjectKPI(data: {
    id: string
    updates: {
      name?: string
      value?: string
      target?: string
      unit?: string
      frequency?: string
      // {{ AURA-X: Add - 添加direction字段支持. Approval: 寸止(ID:1738157400). }}
      direction?: string
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const kpi = await this.prisma.projectKPI.update({
        where: { id: data.id },
        data: {
          ...data.updates,
          updatedAt: new Date()
        }
      })

      return {
        success: true,
        data: kpi
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update project KPI'
      }
    }
  }

  async deleteProjectKPI(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.projectKPI.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete project KPI'
      }
    }
  }

  /**
   * KPI Record operations
   */
  async createKPIRecord(data: {
    kpiId: string
    value: string
    note?: string
    recordedAt?: Date
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const record = await this.prisma.kPIRecord.create({
        data: {
          kpiId: data.kpiId,
          value: data.value,
          note: data.note,
          recordedAt: data.recordedAt || new Date()
        }
      })

      // 同时更新KPI的当前值
      await this.prisma.projectKPI.update({
        where: { id: data.kpiId },
        data: {
          value: data.value,
          updatedAt: new Date()
        }
      })

      return {
        success: true,
        data: record
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create KPI record'
      }
    }
  }

  async getKPIRecords(kpiId: string, limit?: number): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const records = await this.prisma.kPIRecord.findMany({
        where: { kpiId },
        orderBy: { recordedAt: 'desc' },
        take: limit || 50
      })

      return {
        success: true,
        data: records
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch KPI records'
      }
    }
  }

  async updateKPIRecord(data: {
    id: string
    updates: {
      value?: string
      note?: string
      recordedAt?: Date
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const record = await this.prisma.kPIRecord.update({
        where: { id: data.id },
        data: data.updates
      })

      return {
        success: true,
        data: record
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update KPI record'
      }
    }
  }

  async deleteKPIRecord(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.kPIRecord.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete KPI record'
      }
    }
  }

  /**
   * Area Metric operations
   */
  async createAreaMetric(data: {
    areaId: string
    name: string
    value: string
    target?: string
    unit?: string
    frequency?: string
    // {{ AURA-X: Add - 扩展创建参数支持新字段. Approval: 寸止(ID:1738157400). }}
    trackingType?: string
    habitConfig?: any
    standardConfig?: any
    isActive?: boolean
    priority?: string
    category?: string
    description?: string
    // {{ AURA-X: Add - 添加direction字段支持. Approval: 寸止(ID:1738157400). }}
    direction?: string
    relatedHabits?: string[]
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const metric = await this.prisma.areaMetric.create({
        data: {
          areaId: data.areaId,
          name: data.name,
          value: data.value,
          target: data.target,
          unit: data.unit,
          frequency: data.frequency,
          // {{ AURA-X: Add - 包含新字段的创建逻辑. Approval: 寸止(ID:1738157400). }}
          trackingType: data.trackingType || 'metric',
          habitConfig: data.habitConfig,
          standardConfig: data.standardConfig,
          isActive: data.isActive ?? true,
          priority: data.priority,
          category: data.category,
          description: data.description,
          // {{ AURA-X: Add - 包含direction字段. Approval: 寸止(ID:1738157400). }}
          direction: data.direction || 'increase',
          relatedHabits: data.relatedHabits && data.relatedHabits.length > 0 ? data.relatedHabits : null
        }
      })

      return {
        success: true,
        data: metric
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create area metric'
      }
    }
  }

  async getAreaMetrics(areaId: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const metrics = await this.prisma.areaMetric.findMany({
        where: { areaId },
        orderBy: { updatedAt: 'desc' }
      })

      return {
        success: true,
        data: metrics
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch area metrics'
      }
    }
  }

  async updateAreaMetric(data: {
    id: string
    updates: {
      name?: string
      value?: string
      target?: string
      unit?: string
      frequency?: string
      // {{ AURA-X: Add - 扩展更新参数支持新字段. Approval: 寸止(ID:1738157400). }}
      trackingType?: string
      habitConfig?: any
      standardConfig?: any
      isActive?: boolean
      priority?: string
      category?: string
      description?: string
      // {{ AURA-X: Add - 添加direction字段支持. Approval: 寸止(ID:1738157400). }}
      direction?: string
      relatedHabits?: string[]
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const metric = await this.prisma.areaMetric.update({
        where: { id: data.id },
        data: {
          ...data.updates,
          updatedAt: new Date()
        }
      })

      return {
        success: true,
        data: metric
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update area metric'
      }
    }
  }

  async deleteAreaMetric(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.areaMetric.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete area metric'
      }
    }
  }

  /**
   * Area Metric Record operations
   */
  async createAreaMetricRecord(data: {
    metricId: string
    value: string
    note?: string
    recordedAt?: Date
    // {{ AURA-X: Add - 扩展记录创建参数支持新字段. Approval: 寸止(ID:1738157400). }}
    mood?: string
    energy?: string
    context?: any
    tags?: string[]
    quality?: string
    duration?: number
    difficulty?: string
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const record = await this.prisma.areaMetricRecord.create({
        data: {
          metricId: data.metricId,
          value: data.value,
          note: data.note,
          recordedAt: data.recordedAt || new Date(),
          // {{ AURA-X: Add - 包含新字段的记录创建逻辑. Approval: 寸止(ID:1738157400). }}
          mood: data.mood,
          energy: data.energy,
          context: data.context,
          tags: data.tags || null,
          quality: data.quality,
          duration: data.duration,
          difficulty: data.difficulty
        }
      })

      // 同时更新AreaMetric的当前值
      await this.prisma.areaMetric.update({
        where: { id: data.metricId },
        data: {
          value: data.value,
          updatedAt: new Date()
        }
      })

      return {
        success: true,
        data: record
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create area metric record'
      }
    }
  }

  async getAreaMetricRecords(metricId: string, limit?: number): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const records = await this.prisma.areaMetricRecord.findMany({
        where: { metricId },
        orderBy: { recordedAt: 'desc' },
        take: limit || 50
      })

      return {
        success: true,
        data: records
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch area metric records'
      }
    }
  }

  async updateAreaMetricRecord(data: {
    id: string
    updates: {
      value?: string
      note?: string
      recordedAt?: Date
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const record = await this.prisma.areaMetricRecord.update({
        where: { id: data.id },
        data: data.updates
      })

      return {
        success: true,
        data: record
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update area metric record'
      }
    }
  }

  async deleteAreaMetricRecord(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.areaMetricRecord.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete area metric record'
      }
    }
  }

  /**
   * Deliverable operations
   */
  async createDeliverable(data: {
    projectId: string
    title: string
    description?: string
    type?: string
    status?: string
    priority?: string
    plannedDate?: Date
    deadline?: Date
    content?: string
    attachments?: any
    metrics?: any
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const deliverable = await this.prisma.deliverable.create({
        data: {
          projectId: data.projectId,
          title: data.title,
          description: data.description,
          type: data.type || 'document',
          status: data.status || 'planned',
          priority: data.priority,
          plannedDate: data.plannedDate,
          deadline: data.deadline,
          content: data.content,
          attachments: data.attachments,
          metrics: data.metrics
        },
        include: {
          project: true,
          tasks: true,
          resources: true
        }
      })

      return {
        success: true,
        data: deliverable
      }
    } catch (error) {
      console.error('Failed to create deliverable:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create deliverable'
      }
    }
  }

  async getProjectDeliverables(projectId: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const deliverables = await this.prisma.deliverable.findMany({
        where: {
          projectId: projectId
        },
        include: {
          project: true,
          tasks: true,
          resources: true
        },
        orderBy: [
          { status: 'asc' },
          { priority: 'desc' },
          { createdAt: 'desc' }
        ]
      })

      return {
        success: true,
        data: deliverables
      }
    } catch (error) {
      console.error('Failed to get project deliverables:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get project deliverables'
      }
    }
  }

  async updateDeliverable(data: {
    id: string
    updates: {
      title?: string
      description?: string
      type?: string
      status?: string
      priority?: string
      plannedDate?: Date
      actualDate?: Date
      deadline?: Date
      content?: string
      attachments?: any
      metrics?: any
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const deliverable = await this.prisma.deliverable.update({
        where: { id: data.id },
        data: {
          ...data.updates,
          updatedAt: new Date()
        },
        include: {
          project: true,
          tasks: true,
          resources: true
        }
      })

      return {
        success: true,
        data: deliverable
      }
    } catch (error) {
      console.error('Failed to update deliverable:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update deliverable'
      }
    }
  }

  async deleteDeliverable(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.deliverable.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      console.error('Failed to delete deliverable:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete deliverable'
      }
    }
  }

  /**
   * Resource linking operations
   */
  async getProjectResources(projectId: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const resources = await this.prisma.resourceLink.findMany({
        where: {
          projectId: projectId
        },
        orderBy: {
          title: 'asc'
        }
      })

      return {
        success: true,
        data: resources
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get project resources'
      }
    }
  }

  async linkResourceToProject(data: {
    resourceId: string
    projectId: string
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      // Update the resource to link it to the project
      const resource = await this.prisma.resourceLink.update({
        where: { id: data.resourceId },
        data: { projectId: data.projectId }
      })

      return {
        success: true,
        data: resource
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to link resource to project'
      }
    }
  }

  async unlinkResourceFromProject(resourceId: string, projectId: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      // Remove the project link from the resource
      await this.prisma.resourceLink.update({
        where: {
          id: resourceId,
          projectId: projectId
        },
        data: { projectId: null }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to unlink resource from project'
      }
    }
  }

  async getAreaResources(areaId: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const resources = await this.prisma.resourceLink.findMany({
        where: { areaId },
        orderBy: { id: 'desc' }
      })

      return {
        success: true,
        data: resources
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get area resources'
      }
    }
  }

  async unlinkResourceFromArea(resourceId: string, areaId: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      // Remove the area link from the resource
      await this.prisma.resourceLink.update({
        where: {
          id: resourceId,
          areaId: areaId
        },
        data: { areaId: null }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to unlink resource from area'
      }
    }
  }

  // Habit management methods
  async getHabitsByArea(areaId: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const habits = await this.prisma.habit.findMany({
        where: { areaId },
        orderBy: { createdAt: 'desc' }
      })

      return {
        success: true,
        data: habits
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch habits'
      }
    }
  }

  async createHabit(data: { name: string; areaId: string; description?: string }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const habit = await this.prisma.habit.create({
        data: {
          name: data.name,
          description: data.description || null,
          areaId: data.areaId,
          frequency: 'Daily',
          archived: false
        }
      })

      return {
        success: true,
        data: habit
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create habit'
      }
    }
  }
}

// Export singleton instance
export const databaseService = new DatabaseService()
export default databaseService
