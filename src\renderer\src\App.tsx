import { RouterProvider } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { router } from './lib/router'
import { ErrorBoundary } from './components/shared'
import { LanguageProvider } from './contexts/LanguageContext'
import { FirstTimeSetup } from './components/features/FirstTimeSetup'
import { useUserSettingsStore } from './store/userSettingsStore'
import { useProjectStore } from './store/projectStore'
import { useAreaStore } from './store/areaStore'
import { fileSystemApi } from './lib/api'


function App(): React.JSX.Element {
  const { settings, isInitialized } = useUserSettingsStore()
  const { fetchProjects } = useProjectStore()
  const { fetchAreas } = useAreaStore()
  const [showFirstTimeSetup, setShowFirstTimeSetup] = useState(false)
  const [hasInitialized, setHasInitialized] = useState(false)

  useEffect(() => {
    // 检查是否是首次使用
    if (settings.isFirstTime && !isInitialized) {
      setShowFirstTimeSetup(true)
    } else if (!settings.isFirstTime && settings.workspaceDirectory && !hasInitialized) {
      // 只在第一次时初始化，避免重复执行
      setHasInitialized(true)

      // 如果不是首次使用且有工作目录设置，重新初始化文件系统
      if (window.electronAPI?.fileSystem?.reinitialize) {
        fileSystemApi.reinitialize(settings.workspaceDirectory).catch((error) => {
          console.error('Failed to reinitialize file system on startup:', error)
        })
      }

      // 加载项目和领域数据
      fetchProjects().catch((error) => {
        console.error('Failed to fetch projects on startup:', error)
      })
      fetchAreas().catch((error) => {
        console.error('Failed to fetch areas on startup:', error)
      })


    }
  }, [
    settings.isFirstTime,
    settings.workspaceDirectory,
    isInitialized,
    hasInitialized,
    fetchProjects,
    fetchAreas
  ])

  const handleFirstTimeSetupComplete = () => {
    setShowFirstTimeSetup(false)
  }

  return (
    <ErrorBoundary>
      <LanguageProvider>
        <RouterProvider router={router} />
        <FirstTimeSetup isOpen={showFirstTimeSetup} onComplete={handleFirstTimeSetupComplete} />
      </LanguageProvider>
    </ErrorBoundary>
  )
}

export default App
