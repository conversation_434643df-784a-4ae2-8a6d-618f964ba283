/**
 * 领域高级分析组件
 * 提供深度数据洞察、趋势预测和智能建议
 */

import { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  Lightbulb,
  Award,
  AlertTriangle
} from 'lucide-react'
import { cn } from '../../lib/utils'
import { databaseApi } from '../../lib/api'
import { useLanguage } from '../../contexts/LanguageContext'
// {{ AURA-X: Add - 导入双向KPI计算函数. Approval: 寸止(ID:1738157400). }}
import { calculateMetricProgress } from '../../lib/areaMetricAdapter'
import type { AreaMetric, AreaMetricRecord } from '../../../../shared/types'

interface AreaAnalyticsProps {
  areaId: string
  metrics: AreaMetric[]
  className?: string
}

interface AnalyticsData {
  totalMetrics: number
  activeMetrics: number
  habitMetrics: number
  standardMetrics: number
  averageProgress: number
  topPerformers: AreaMetric[]
  needsAttention: AreaMetric[]
  weeklyTrends: { [key: string]: number }
  categoryDistribution: { [key: string]: number }
  priorityDistribution: { [key: string]: number }
  insights: string[]
  recommendations: string[]
}

export function AreaAnalytics({ areaId, metrics, className }: AreaAnalyticsProps) {
  const { t } = useLanguage()
  const [records, setRecords] = useState<{ [metricId: string]: AreaMetricRecord[] }>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadAllRecords()
  }, [metrics])

  const loadAllRecords = async () => {
    setLoading(true)
    try {
      const recordsData: { [metricId: string]: AreaMetricRecord[] } = {}
      
      for (const metric of metrics) {
        const result = await databaseApi.getAreaMetricRecords(metric.id, 100)
        if (result.success) {
          recordsData[metric.id] = result.data || []
        }
      }
      
      setRecords(recordsData)
    } catch (error) {
      console.error('Failed to load records:', error)
    } finally {
      setLoading(false)
    }
  }

  const analyticsData = useMemo((): AnalyticsData => {
    if (metrics.length === 0) {
      return {
        totalMetrics: 0,
        activeMetrics: 0,
        habitMetrics: 0,
        standardMetrics: 0,
        averageProgress: 0,
        topPerformers: [],
        needsAttention: [],
        weeklyTrends: {},
        categoryDistribution: {},
        priorityDistribution: {},
        insights: [],
        recommendations: []
      }
    }

    // 基础统计
    const activeMetrics = metrics.filter(m => (m as any).isActive !== false)
    const habitMetrics = metrics.filter(m => (m as any).trackingType === 'habit')
    const standardMetrics = metrics.filter(m => (m as any).trackingType === 'standard')

    // {{ AURA-X: Modify - 使用双向KPI进度计算. Approval: 寸止(ID:1738157400). }}
    // 进度计算，支持增长型和减少型指标
    const metricsWithProgress = metrics.map(metric => {
      if (!metric.target) return { metric, progress: 0 }
      return { metric, progress: calculateMetricProgress(metric) }
    })

    const averageProgress = metricsWithProgress.length > 0
      ? metricsWithProgress.reduce((sum, item) => sum + item.progress, 0) / metricsWithProgress.length
      : 0

    // 顶级表现者和需要关注的指标
    const sortedByProgress = metricsWithProgress.sort((a, b) => b.progress - a.progress)
    const topPerformers = sortedByProgress.slice(0, 3).map(item => item.metric)
    const needsAttention = sortedByProgress.slice(-3).reverse().map(item => item.metric)

    // 周趋势分析
    const weeklyTrends: { [key: string]: number } = {}
    const now = new Date()
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now)
      date.setDate(date.getDate() - i)
      const dateKey = date.toISOString().split('T')[0]
      
      let dayTotal = 0
      metrics.forEach(metric => {
        const metricRecords = records[metric.id] || []
        const dayRecords = metricRecords.filter(record => 
          record.recordedAt.toString().startsWith(dateKey)
        )
        dayTotal += dayRecords.length
      })
      
      weeklyTrends[dateKey] = dayTotal
    }

    // 分类分布
    const categoryDistribution: { [key: string]: number } = {}
    metrics.forEach(metric => {
      const category = (metric as any).category || 'uncategorized'
      categoryDistribution[category] = (categoryDistribution[category] || 0) + 1
    })

    // 优先级分布
    const priorityDistribution: { [key: string]: number } = {}
    metrics.forEach(metric => {
      const priority = (metric as any).priority || 'medium'
      priorityDistribution[priority] = (priorityDistribution[priority] || 0) + 1
    })

    // 生成洞察
    const insights: string[] = []
    const recommendations: string[] = []

    if (habitMetrics.length > 0) {
      // {{ AURA-X: Modify - 使用双向KPI进度计算. Approval: 寸止(ID:1738157400). }}
      const habitProgress = habitMetrics.reduce((sum, metric) => {
        return sum + calculateMetricProgress(metric)
      }, 0) / habitMetrics.length

      if (habitProgress > 80) {
        insights.push(t('pages.areas.detail.kpiManagement.areaAnalytics.excellentHabitConsistency', { count: habitMetrics.length }))
      } else if (habitProgress < 50) {
        insights.push(t('pages.areas.detail.kpiManagement.areaAnalytics.habitFormationNeedsAttention'))
        recommendations.push(t('pages.areas.detail.kpiManagement.areaAnalytics.focusOnCoreHabits'))
      }
    }

    if (averageProgress > 90) {
      insights.push(t('pages.areas.detail.kpiManagement.areaAnalytics.outstandingPerformance'))
      recommendations.push(t('pages.areas.detail.kpiManagement.areaAnalytics.considerChallengingTargets'))
    } else if (averageProgress < 60) {
      insights.push(t('pages.areas.detail.kpiManagement.areaAnalytics.severalMetricsBelowTarget'))
      recommendations.push(t('pages.areas.detail.kpiManagement.areaAnalytics.identifyTopPriorityMetrics'))
    }

    // 活动频率分析
    const totalRecords = Object.values(records).flat().length
    const avgRecordsPerMetric = totalRecords / metrics.length
    if (avgRecordsPerMetric < 5) {
      recommendations.push(t('pages.areas.detail.kpiManagement.areaAnalytics.increaseTrackingFrequency'))
    }

    return {
      totalMetrics: metrics.length,
      activeMetrics: activeMetrics.length,
      habitMetrics: habitMetrics.length,
      standardMetrics: standardMetrics.length,
      averageProgress: Math.round(averageProgress),
      topPerformers,
      needsAttention,
      weeklyTrends,
      categoryDistribution,
      priorityDistribution,
      insights,
      recommendations
    }
  }, [metrics, records])

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-8">
          <Activity className="h-8 w-8 mx-auto mb-2 animate-pulse" />
          <p>{t('pages.areas.detail.kpiManagement.areaAnalytics.analyzingData')}</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn('space-y-6', className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            {t('pages.areas.detail.kpiManagement.areaAnalytics.title')}
          </CardTitle>
          <CardDescription>
            {t('pages.areas.detail.kpiManagement.areaAnalytics.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">{t('pages.areas.detail.kpiManagement.areaAnalytics.tabs.overview')}</TabsTrigger>
              <TabsTrigger value="trends">{t('pages.areas.detail.kpiManagement.areaAnalytics.tabs.trends')}</TabsTrigger>
              <TabsTrigger value="distribution">{t('pages.areas.detail.kpiManagement.areaAnalytics.tabs.distribution')}</TabsTrigger>
              <TabsTrigger value="insights">{t('pages.areas.detail.kpiManagement.areaAnalytics.tabs.insights')}</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              {/* 关键指标卡片 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold">{analyticsData.totalMetrics}</div>
                    <p className="text-sm text-muted-foreground">{t('pages.areas.detail.kpiManagement.areaAnalytics.totalMetrics')}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">{analyticsData.activeMetrics}</div>
                    <p className="text-sm text-muted-foreground">{t('pages.areas.detail.kpiManagement.areaAnalytics.active')}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">{analyticsData.habitMetrics}</div>
                    <p className="text-sm text-muted-foreground">{t('pages.areas.detail.kpiManagement.areaAnalytics.habits')}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600">{analyticsData.averageProgress}%</div>
                    <p className="text-sm text-muted-foreground">{t('pages.areas.detail.kpiManagement.areaAnalytics.avgProgress')}</p>
                  </CardContent>
                </Card>
              </div>

              {/* 进度条 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">{t('pages.areas.detail.kpiManagement.areaAnalytics.overallProgress')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <Progress value={analyticsData.averageProgress} className="h-3" />
                  <div className="flex justify-between text-sm text-muted-foreground mt-2">
                    <span>0%</span>
                    <span className="font-medium">{analyticsData.averageProgress}% {t('pages.areas.detail.kpiManagement.areaAnalytics.complete')}</span>
                    <span>100%</span>
                  </div>
                </CardContent>
              </Card>

              {/* 顶级表现者和需要关注 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Award className="h-5 w-5 text-yellow-500" />
                      {t('pages.areas.detail.kpiManagement.areaAnalytics.topPerformers')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {analyticsData.topPerformers.length === 0 ? (
                      <p className="text-sm text-muted-foreground">{t('pages.areas.detail.kpiManagement.areaAnalytics.noDataAvailable')}</p>
                    ) : (
                      analyticsData.topPerformers.map((metric, index) => (
                        <div key={metric.id} className="flex items-center justify-between">
                          <span className="text-sm">{metric.name}</span>
                          <Badge variant="secondary" className="text-xs">
                            #{index + 1}
                          </Badge>
                        </div>
                      ))
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-orange-500" />
                      {t('pages.areas.detail.kpiManagement.areaAnalytics.needsAttention')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {analyticsData.needsAttention.length === 0 ? (
                      <p className="text-sm text-muted-foreground">{t('pages.areas.detail.kpiManagement.areaAnalytics.allMetricsOnTrack')}</p>
                    ) : (
                      analyticsData.needsAttention.map((metric) => (
                        <div key={metric.id} className="flex items-center justify-between">
                          <span className="text-sm">{metric.name}</span>
                          <Badge variant="outline" className="text-xs text-orange-600">
                            {t('pages.areas.detail.kpiManagement.areaAnalytics.review')}
                          </Badge>
                        </div>
                      ))
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="trends" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">{t('pages.areas.detail.kpiManagement.areaAnalytics.weeklyActivityTrends')}</CardTitle>
                  <CardDescription>
                    {t('pages.areas.detail.kpiManagement.areaAnalytics.weeklyTrendsDesc')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(analyticsData.weeklyTrends).map(([date, count]) => (
                      <div key={date} className="flex items-center gap-4">
                        <div className="w-20 text-sm text-muted-foreground">
                          {new Date(date).toLocaleDateString('en', { weekday: 'short', month: 'short', day: 'numeric' })}
                        </div>
                        <div className="flex-1">
                          <Progress value={(count / Math.max(...Object.values(analyticsData.weeklyTrends))) * 100} className="h-2" />
                        </div>
                        <div className="w-8 text-sm font-medium">{count}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="distribution" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">{t('pages.areas.detail.kpiManagement.areaAnalytics.categoryDistribution')}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {Object.entries(analyticsData.categoryDistribution).map(([category, count]) => (
                      <div key={category} className="flex items-center justify-between">
                        <span className="text-sm capitalize">{t(`pages.areas.detail.categories.${category}`) || category}</span>
                        <div className="flex items-center gap-2">
                          <Progress value={(count / analyticsData.totalMetrics) * 100} className="w-20 h-2" />
                          <span className="text-sm font-medium w-8">{count}</span>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">{t('pages.areas.detail.kpiManagement.areaAnalytics.priorityDistribution')}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {Object.entries(analyticsData.priorityDistribution).map(([priority, count]) => (
                      <div key={priority} className="flex items-center justify-between">
                        <span className="text-sm capitalize">{t(`pages.areas.detail.priorities.${priority}`) || priority}</span>
                        <div className="flex items-center gap-2">
                          <Progress value={(count / analyticsData.totalMetrics) * 100} className="w-20 h-2" />
                          <span className="text-sm font-medium w-8">{count}</span>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="insights" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Lightbulb className="h-5 w-5 text-yellow-500" />
                      {t('pages.areas.detail.kpiManagement.areaAnalytics.keyInsights')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {analyticsData.insights.length === 0 ? (
                      <p className="text-sm text-muted-foreground">
                        {t('pages.areas.detail.kpiManagement.areaAnalytics.addMoreDataForInsights')}
                      </p>
                    ) : (
                      analyticsData.insights.map((insight, index) => (
                        <div key={index} className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                          <p className="text-sm">{insight}</p>
                        </div>
                      ))
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Target className="h-5 w-5 text-green-500" />
                      {t('pages.areas.detail.kpiManagement.areaAnalytics.recommendations')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {analyticsData.recommendations.length === 0 ? (
                      <p className="text-sm text-muted-foreground">
                        {t('pages.areas.detail.kpiManagement.areaAnalytics.greatJobKeepUp')}
                      </p>
                    ) : (
                      analyticsData.recommendations.map((recommendation, index) => (
                        <div key={index} className="p-3 bg-green-50 rounded-lg border-l-4 border-green-400">
                          <p className="text-sm">{recommendation}</p>
                        </div>
                      ))
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

export default AreaAnalytics
