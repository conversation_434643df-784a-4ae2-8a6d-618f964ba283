import { useState, useMemo, useEffect } from 'react'
import { useParams, Link, useNavigate, useLocation } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Progress } from '../ui/progress'
import { Textarea } from '../ui/textarea'
// {{ AURA-X: Remove - 移除不再使用的Checkbox导入. Approval: 寸止(ID:1738157400). }}
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { cn } from '../../lib/utils'
import { useAreaStore } from '../../store/areaStore'
import { useProjectStore } from '../../store/projectStore'
// {{ AURA-X: Add - 导入任务存储用于清单管理. Approval: 寸止(ID:1738157400). }}
import { useTaskStore } from '../../store/taskStore'
import { useLanguage } from '../../contexts/LanguageContext'
import { useConfirmDialog } from '../shared/ConfirmDialog'
import CreateAreaDialog from './CreateAreaDialog'
import ProjectCard from './ProjectCard'
import HabitItem from './HabitItem'
import CreateHabitDialog from './CreateHabitDialog'
// 使用最终版本的KPI管理组件
import AreaKPIManagement from './AreaKPIManagement'
// {{ AURA-X: Add - 导入清单模板管理组件. Approval: 寸止(ID:1738157400). }}
import ChecklistTemplateManager from './ChecklistTemplateManager'
// {{ AURA-X: Add - 导入关联资源管理组件. Approval: 寸止(ID:1738157400). }}
import AreaResourcesManager from './AreaResourcesManager'
// {{ AURA-X: Add - 导入Markdown编辑器. Approval: 寸止(ID:1738157400). }}
import MarkdownEditor from './MarkdownEditor'
// {{ AURA-X: Add - 导入数据可视化仪表板. Approval: 寸止(ID:1738157400). }}
import AreaDashboard from './AreaDashboard'
// {{ AURA-X: Add - 导入响应式布局组件. Approval: 寸止(ID:1738157400). }}
import { ResponsiveLayout, ResponsiveGrid, useResponsive } from '../layout/ResponsiveLayout'
// {{ AURA-X: Add - 导入导入导出组件. Approval: 寸止(ID:1738157400). }}
import AreaImportExport from './AreaImportExport'
// {{ AURA-X: Add - 导入创建项目对话框. Approval: 寸止(ID:1738157400). }}
import CreateProjectDialog from './CreateProjectDialog'
import type { Area } from '../../../../shared/types'

export function AreaDetailPage() {
  const { areaId } = useParams<{ areaId: string }>()
  const navigate = useNavigate()
  const location = useLocation()
  const isArchived = location.state?.archived || false
  const {
    areas,
    updateArea,
    deleteArea,
    archiveArea,
    // {{ AURA-X: Add - 集成习惯管理功能. Approval: 寸止(ID:1738157400). }}
    habits,
    addHabit,
    updateHabit,
    deleteHabit: deleteHabitFromStore,
    habitRecords,
    toggleHabitRecord
  } = useAreaStore()
  const { projects, addProject } = useProjectStore()
  // {{ AURA-X: Add - 获取任务数据用于显示关联任务. Approval: 寸止(ID:1738157400). }}
  const { tasks } = useTaskStore()
  const { t } = useLanguage()
  const { confirm, ConfirmDialog: ConfirmDialogComponent } = useConfirmDialog()

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isCreateHabitDialogOpen, setIsCreateHabitDialogOpen] = useState(false)
  const [editingHabit, setEditingHabit] = useState<any>(null)
  // {{ AURA-X: Add - 添加创建项目对话框状态. Approval: 寸止(ID:1738157400). }}
  const [isCreateProjectDialogOpen, setIsCreateProjectDialogOpen] = useState(false)
  // {{ AURA-X: Modify - 移除本地习惯状态，使用全局状态. Approval: 寸止(ID:1738157400). }}
  const [notes, setNotes] = useState('')
  const [isNotesEditing, setIsNotesEditing] = useState(false)
  // {{ AURA-X: Add - 添加临时笔记状态避免编辑器循环更新. Approval: 寸止(ID:1738157400). }}
  const [tempNotes, setTempNotes] = useState('')
  // {{ AURA-X: Add - 添加领域标准编辑状态管理. Approval: 寸止(ID:1738157400). }}
  const [isStandardEditing, setIsStandardEditing] = useState(false)
  const [standardText, setStandardText] = useState('')
  // {{ AURA-X: Remove - 移除简单清单状态，使用模板系统. Approval: 寸止(ID:1738157400). }}

  const area = areas.find((a) => a.id === areaId)
  const relatedProjects = projects.filter(
    (project) => project.areaId === areaId && !project.archived
  )

  // {{ AURA-X: Add - 计算与该领域关联的任务. Approval: 寸止(ID:1738157400). }}
  const areaTasks = tasks.filter(task => task.areaId === areaId && !task.completed)

  // {{ AURA-X: Add - 初始化领域标准文本. Approval: 寸止(ID:1738157400). }}
  useEffect(() => {
    if (area && area.standard !== undefined) {
      setStandardText(area.standard || '')
    }
  }, [area])

  // {{ AURA-X: Add - 页面加载时滚动到顶部. Approval: 寸止(ID:1738157400). }}
  useEffect(() => {
    const scrollToTop = () => {
      // 找到真正的滚动容器 - Layout 中的主内容滚动容器
      const scrollContainer = document.querySelector('main .overflow-y-auto')
      if (scrollContainer) {
        scrollContainer.scrollTop = 0
      }

      // 备用方案：重置其他可能的滚动容器
      window.scrollTo({ top: 0, left: 0, behavior: 'instant' })
      document.documentElement.scrollTop = 0
      document.body.scrollTop = 0
    }

    // 立即执行
    scrollToTop()

    // 延迟执行，确保 DOM 完全更新
    const timer1 = setTimeout(scrollToTop, 0)
    const timer2 = setTimeout(scrollToTop, 100)

    return () => {
      clearTimeout(timer1)
      clearTimeout(timer2)
    }
  }, [areaId])

  if (!area) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <div className="text-4xl mb-4">🏠</div>
          <h2 className="text-xl font-semibold mb-2">{t('pages.areas.detail.notFound')}</h2>
          <p className="text-muted-foreground mb-4">
            {t('pages.areas.detail.notFoundDescription')}
          </p>
          <Button asChild>
            <Link to="/areas">{t('pages.areas.detail.backToAreasButton')}</Link>
          </Button>
        </div>
      </div>
    )
  }

  // {{ AURA-X: Modify - 使用全局习惯数据计算统计. Approval: 寸止(ID:1738157400). }}
  // Calculate area statistics
  const areaHabits = habits.filter(habit => habit.areaId === areaId)

  // {{ AURA-X: Add - 适配习惯数据格式. Approval: 寸止(ID:1738157400). }}
  // Convert database habits to HabitItem format
  const adaptedHabits = areaHabits.map(habit => ({
    ...habit,
    color: '#3b82f6', // Default color
    frequency: habit.frequency as 'daily' | 'weekly' | 'monthly', // Type assertion
    records: habitRecords
      .filter(record => record.habitId === habit.id)
      .map(record => ({
        date: (record.date instanceof Date ? record.date : new Date(record.date)).toISOString().split('T')[0],
        completed: record.completed,
        value: undefined,
        note: undefined
      })),
    createdAt: (habit.createdAt instanceof Date ? habit.createdAt : new Date(habit.createdAt)).toISOString(),
    updatedAt: (habit as any).updatedAt
      ? ((habit as any).updatedAt instanceof Date ? (habit as any).updatedAt : new Date((habit as any).updatedAt)).toISOString()
      : new Date().toISOString()
  }))
  // {{ AURA-X: Modify - 更新统计计算，移除简单清单引用. Approval: 寸止(ID:1738157400). }}
  const stats = useMemo(() => {
    const totalProjects = relatedProjects.length
    const completedProjects = relatedProjects.filter((p) => p.status === 'Completed').length
    const activeHabits = areaHabits.length
    // 使用模板系统的清单实例计算进度
    const { checklistInstances } = useTaskStore.getState()
    const areaInstances = checklistInstances.filter(instance => {
      const { checklists } = useTaskStore.getState()
      const template = checklists.find(c => c.id === instance.checklistId)
      return template && (template as any).areaId === areaId
    })
    const completedInstances = areaInstances.filter(instance => instance.completedAt).length
    const checklistProgress = areaInstances.length > 0 ? Math.round((completedInstances / areaInstances.length) * 100) : 0

    return {
      totalProjects,
      completedProjects,
      activeHabits,
      checklistProgress,
      completedChecklist: completedInstances,
      totalChecklist: areaInstances.length
    }
  }, [relatedProjects, areaHabits.length, areaId])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'Needs Attention':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'On Hold':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'Review Required':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const handleEditArea = async (areaData: Omit<Area, 'id' | 'createdAt' | 'updatedAt'>) => {
    updateArea(area.id, {
      ...areaData,
      updatedAt: new Date()
    })
    setIsEditDialogOpen(false)
  }

  const handleDeleteArea = () => {
    confirm({
      title: '删除领域',
      description: `确定要删除"${area.name}"吗？此操作无法撤销。`,
      variant: 'destructive',
      confirmText: '删除',
      cancelText: '取消',
      onConfirm: () => {
        deleteArea(area.id)
        navigate('/areas')
      }
    })
  }

  const handleArchiveArea = () => {
    confirm({
      title: '归档领域',
      description: `归档"${area.name}"？您可以稍后从归档中恢复。`,
      variant: 'warning',
      confirmText: '归档',
      cancelText: '取消',
      onConfirm: async () => {
        await archiveArea(area.id)
        navigate('/areas')
      }
    })
  }

  // {{ AURA-X: Modify - 修复笔记保存逻辑，使用临时状态. Approval: 寸止(ID:1738157400). }}
  const handleSaveNotes = () => {
    setNotes(tempNotes)
    setIsNotesEditing(false)
  }

  const handleStartEditNotes = () => {
    setTempNotes(notes)
    setIsNotesEditing(true)
  }

  // {{ AURA-X: Add - 添加项目创建处理函数. Approval: 寸止(ID:1738157400). }}
  const handleCreateProject = (projectData: any) => {
    const newProject = {
      ...projectData,
      id: `project-${Date.now()}`,
      areaId: areaId,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    addProject(newProject)
    setIsCreateProjectDialogOpen(false)
  }

  // {{ AURA-X: Add - 添加领域标准保存功能. Approval: 寸止(ID:1738157400). }}
  const handleSaveStandard = () => {
    updateArea(area.id, { standard: standardText.trim() })
    setIsStandardEditing(false)
  }

  // {{ AURA-X: Modify - 使用全局状态管理习惯. Approval: 寸止(ID:1738157400). }}
  const handleCreateHabit = async (habitData: any) => {
    const newHabit = {
      ...habitData,
      id: `habit-${Date.now()}`,
      areaId: area.id,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    addHabit(newHabit)
  }

  const handleEditHabit = async (habitData: any) => {
    if (editingHabit) {
      updateHabit(editingHabit.id, {
        ...habitData,
        updatedAt: new Date()
      })
      setEditingHabit(null)
    }
  }

  const handleDeleteHabit = (habitId: string) => {
    confirm({
      title: '删除习惯',
      description: '确定要删除这个习惯吗？此操作无法撤销。',
      variant: 'destructive',
      confirmText: '删除',
      cancelText: '取消',
      onConfirm: () => {
        deleteHabitFromStore(habitId)
      }
    })
  }

  // {{ AURA-X: Modify - 使用全局状态管理习惯记录. Approval: 寸止(ID:1738157400). }}
  const handleToggleHabit = (habitId: string, date: string, completed: boolean) => {
    const dateObj = new Date(date)
    toggleHabitRecord(habitId, dateObj)
  }

  // {{ AURA-X: Remove - 移除不再使用的清单处理函数. Approval: 寸止(ID:1738157400). }}

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <Button variant="ghost" size="sm" asChild>
              <Link to="/areas" className="text-muted-foreground hover:text-foreground">
                {t('pages.areas.detail.backToAreas')}
              </Link>
            </Button>
          </div>
        </div>

        {!isArchived && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 5v.01M12 12v.01M12 19v.01"
                  />
                </svg>
                {t('pages.areas.detail.actions')}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setIsEditDialogOpen(true)}>
                {t('pages.areas.detail.editArea')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleArchiveArea}>
                {t('pages.areas.detail.archiveArea')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleDeleteArea}
                className="text-red-600 focus:text-red-600"
              >
                {t('pages.areas.detail.deleteArea')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {/* 归档横幅提示 */}
      {isArchived && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-medium text-amber-800">
                您正在查看一个已归档的领域
              </h3>
              <p className="text-sm text-amber-700 mt-1">
                此领域处于只读状态，您可以查看所有历史信息，但无法进行编辑操作。
              </p>
            </div>
          </div>
        </div>
      )}

      {/* {{ AURA-X: Add - 添加数据可视化仪表板. Approval: 寸止(ID:1738157400). }} */}
      {/* Area Dashboard */}
      <AreaDashboard
        areaId={areaId!}
        areaName={isArchived ? `${area.name} (Archived)` : area.name}
        areaDescription={area.description || undefined}
        stats={stats}
        habits={adaptedHabits}
        projects={relatedProjects}
      />

      {/* {{ AURA-X: Add - 添加领域标准展示卡片. Approval: 寸止(ID:1738157400). }} */}
      {/* Area Standard */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {t('pages.areas.detail.areaStandard') || '领域标准'}
              </CardTitle>
              <CardDescription>
                {t('pages.areas.detail.areaStandardDescription') || '定义该领域的成功标准和核心原则'}
              </CardDescription>
            </div>
            {!isArchived && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsStandardEditing(!isStandardEditing)}
              >
                {isStandardEditing ? (t('pages.areas.detail.cancel') || '取消') : (t('pages.areas.detail.edit') || '编辑')}
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {isStandardEditing ? (
            <div className="space-y-3">
              <Textarea
                value={standardText}
                onChange={(e) => setStandardText(e.target.value)}
                placeholder={t('pages.areas.detail.standardPlaceholder') || '例如：保持每月储蓄率不低于30%，并按时还清所有账单。'}
                rows={3}
                className="resize-none"
              />
              <div className="flex gap-2">
                <Button size="sm" onClick={handleSaveStandard}>
                  {t('pages.areas.detail.saveStandard') || '保存标准'}
                </Button>
                <Button variant="outline" size="sm" onClick={() => setIsStandardEditing(false)}>
                  {t('pages.areas.detail.cancel') || '取消'}
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-sm">
              {area.standard ? (
                <p className="text-foreground leading-relaxed">{area.standard}</p>
              ) : (
                <p className="text-muted-foreground italic">
                  {t('pages.areas.detail.noStandardSet') || '尚未设置领域标准。点击编辑按钮来定义该领域的成功标准。'}
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Area Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Status and Progress */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{t('pages.areas.detail.areaStatus')}</span>
                <Badge
                  variant="outline"
                  className={cn('text-sm', getStatusColor(area.status || 'Active'))}
                >
                  {t(
                    `pages.areas.filters.status.${(area.status || 'Active').toLowerCase().replace(' ', '')}`
                  ) ||
                    area.status ||
                    'Active'}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>{t('pages.areas.detail.standardsChecklist')}</span>
                  <span className="font-medium">{stats.checklistProgress}%</span>
                </div>
                <Progress value={stats.checklistProgress} className="h-3" />
              </div>

              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
                <div className="space-y-1">
                  <div className="text-2xl font-bold text-blue-600">{stats.totalProjects}</div>
                  <div className="text-xs text-muted-foreground">
                    {t('pages.areas.detail.relatedProjects')}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-2xl font-bold text-green-600">{stats.completedProjects}</div>
                  <div className="text-xs text-muted-foreground">
                    {t('pages.areas.detail.completed')}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-2xl font-bold text-purple-600">{stats.activeHabits}</div>
                  <div className="text-xs text-muted-foreground">
                    {t('pages.areas.detail.activeHabits')}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* {{ AURA-X: Remove - 移除重复的领域标准卡片. Approval: 寸止(ID:1738157400). }} */}

          {/* {{ AURA-X: Replace - 使用增强的清单模板管理器. Approval: 寸止(ID:1738157400). }} */}
          {/* Standards Checklist Templates */}
          <ChecklistTemplateManager areaId={areaId!} />

          {/* {{ AURA-X: Add - 添加关联资源管理器. Approval: 寸止(ID:1738157400). }} */}
          {/* Associated Resources */}
          <AreaResourcesManager areaId={areaId!} />

          {/* Notes */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>{t('pages.areas.detail.areaNotes')}</CardTitle>
                {!isArchived && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => isNotesEditing ? setIsNotesEditing(false) : handleStartEditNotes()}
                  >
                    {isNotesEditing ? t('pages.areas.detail.cancel') : t('pages.areas.detail.edit')}
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {/* {{ AURA-X: Replace - 使用Markdown编辑器替换简单文本框. Approval: 寸止(ID:1738157400). }} */}
              {isNotesEditing ? (
                <div className="space-y-3">
                  <div className="border rounded-md h-[300px]">
                    <MarkdownEditor
                      initialValue={tempNotes}
                      onChange={setTempNotes}
                      height="100%"
                      placeholder={t('pages.areas.detail.notesPlaceholder')}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" onClick={handleSaveNotes}>
                      {t('pages.areas.detail.saveNotes')}
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => {
                      setTempNotes(notes) // 重置临时状态
                      setIsNotesEditing(false)
                    }}>
                      {t('pages.areas.detail.cancel')}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  {notes ? (
                    <div className="prose prose-sm max-w-none">
                      <div dangerouslySetInnerHTML={{ __html: notes.replace(/\n/g, '<br>') }} />
                    </div>
                  ) : (
                    t('pages.areas.detail.noNotesYet')
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Habits */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>{t('pages.areas.detail.areaHabits')}</CardTitle>
                  <CardDescription>{t('pages.areas.detail.areaHabitsDescription')}</CardDescription>
                </div>
                {!isArchived && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsCreateHabitDialogOpen(true)}
                  >
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                    {t('pages.areas.detail.addHabit')}
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {/* {{ AURA-X: Modify - 使用适配后的习惯数据. Approval: 寸止(ID:1738157400). }} */}
              {adaptedHabits.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <div className="text-4xl mb-2">🎯</div>
                  <p className="text-sm">{t('pages.areas.detail.noHabitsYet')}</p>
                  <p className="text-xs mt-1">{t('pages.areas.detail.addFirstHabit')}</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {adaptedHabits.map((habit) => (
                    <div key={habit.id} className="transform scale-95 origin-top">
                      <HabitItem
                        habit={habit}
                        onEdit={isArchived ? undefined : setEditingHabit}
                        onDelete={isArchived ? undefined : handleDeleteHabit}
                        onToggle={isArchived ? undefined : handleToggleHabit}
                      />
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Key Metrics */}
          <AreaKPIManagement
            areaId={areaId!}
          />
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Key Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t('pages.areas.detail.keyInformation')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">
                  {t('pages.areas.detail.reviewFrequency')}
                </span>
                <span>
                  {area.reviewFrequency
                    ? t(
                        `pages.areas.dialog.reviewFrequencyOptions.${area.reviewFrequency.toLowerCase()}`
                      ) || area.reviewFrequency
                    : t('pages.areas.detail.weekly')}
                </span>
              </div>

              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t('pages.areas.detail.created')}</span>
                <span>{new Date(area.createdAt).toLocaleDateString()}</span>
              </div>

              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t('pages.areas.detail.lastUpdated')}</span>
                <span>{new Date(area.updatedAt).toLocaleDateString()}</span>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions - 归档状态下隐藏 */}
          {!isArchived && (
            <Card>
              <CardHeader>
                <CardTitle>{t('pages.areas.detail.quickActions')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => setIsCreateHabitDialogOpen(true)}
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                  {t('pages.areas.detail.addHabit')}
                </Button>
                {/* {{ AURA-X: Add - 添加创建项目按钮. Approval: 寸止(ID:1738157400). }} */}
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => setIsCreateProjectDialogOpen(true)}
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                  创建项目
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                    />
                  </svg>
                  {t('pages.areas.detail.linkProject')}
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                  {t('pages.areas.detail.viewAnalytics')}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* {{ AURA-X: Add - 添加关联任务显示卡片. Approval: 寸止(ID:1738157400). }} */}
          {/* Related Tasks */}
          {areaTasks.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">关联任务</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {areaTasks.slice(0, 5).map((task) => (
                  <div key={task.id} className="flex items-center gap-2 text-sm">
                    <div className={`w-2 h-2 rounded-full ${
                      task.priority === 'high' ? 'bg-red-500' :
                      task.priority === 'medium' ? 'bg-yellow-500' :
                      task.priority === 'low' ? 'bg-green-500' : 'bg-gray-300'
                    }`} />
                    <span className="flex-1 truncate">{task.content}</span>
                    {task.repeatRule && task.repeatRule !== 'none' && (
                      <svg className="w-3 h-3 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    )}
                  </div>
                ))}
                {areaTasks.length > 5 && (
                  <p className="text-xs text-muted-foreground text-center pt-2">
                    还有 {areaTasks.length - 5} 个任务...
                  </p>
                )}
              </CardContent>
            </Card>
          )}

          {/* {{ AURA-X: Add - 添加导入导出功能. Approval: 寸止(ID:1738157400). }} */}
          {/* Import/Export */}
          <AreaImportExport
            areaId={areaId!}
            areaName={area.name}
          />
        </div>
      </div>

      {/* {{ AURA-X: Modify - 相关项目卡片始终显示. Approval: 寸止(ID:1738157400). }} */}
      {/* Related Projects */}
      <Card>
        <CardHeader>
          <CardTitle>{t('pages.areas.detail.relatedProjects')}</CardTitle>
          <CardDescription>{t('pages.areas.detail.relatedProjectsDescription')}</CardDescription>
        </CardHeader>
        <CardContent>
          {relatedProjects.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {relatedProjects.map((project) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  className="max-w-none"
                  fromArea={{ areaId: areaId!, areaName: area.name }}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <div className="text-4xl mb-2">📋</div>
              <p className="text-sm mb-2">暂无关联项目</p>
              <p className="text-xs">点击快速操作中的"创建项目"来添加第一个项目</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Area Dialog */}
      <CreateAreaDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        onSubmit={handleEditArea}
        initialData={area}
      />

      {/* Create/Edit Habit Dialog */}
      <CreateHabitDialog
        isOpen={isCreateHabitDialogOpen || !!editingHabit}
        onClose={() => {
          setIsCreateHabitDialogOpen(false)
          setEditingHabit(null)
        }}
        onSubmit={editingHabit ? handleEditHabit : handleCreateHabit}
        initialData={editingHabit || undefined}
        areaId={area.id}
      />

      {/* {{ AURA-X: Add - 添加创建项目对话框. Approval: 寸止(ID:1738157400). }} */}
      {/* Create Project Dialog */}
      <CreateProjectDialog
        isOpen={isCreateProjectDialogOpen}
        onClose={() => setIsCreateProjectDialogOpen(false)}
        onSubmit={handleCreateProject}
        initialData={{ areaId: areaId }}
      />

      {/* Confirm Dialog */}
      <ConfirmDialogComponent />
    </div>
  )
}

export default AreaDetailPage
