import { useState, useEffect } from 'react'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Label } from '../ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '../ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { Badge } from '../ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Calendar, TrendingUp, Target, Plus, MoreHorizontal, Edit, Trash2, ChevronDown, ChevronUp } from 'lucide-react'
import { cn } from '../../lib/utils'
import { databaseApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
// {{ AURA-X: Add - 导入双向KPI计算函数. Approval: 寸止(ID:1738157400). }}
import { calculateMetricProgress, getMetricDirection } from '../../lib/areaMetricAdapter'
import type { AreaMetric, AreaMetricRecord } from '../../../../shared/types'

interface AreaMetricQuickInputProps {
  metric: AreaMetric
  onRecordCreated?: (record: AreaMetricRecord) => void
  onEdit?: (metric: AreaMetric) => void
  onDelete?: (metric: AreaMetric) => void
  className?: string
}

export function AreaMetricQuickInput({ 
  metric, 
  onRecordCreated, 
  onEdit, 
  onDelete, 
  className 
}: AreaMetricQuickInputProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [value, setValue] = useState('')
  const [note, setNote] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const [historyRecords, setHistoryRecords] = useState<AreaMetricRecord[]>([])
  const [loadingHistory, setLoadingHistory] = useState(false)
  const { addNotification } = useUIStore()

  // 格式化相对时间
  const formatRelativeTime = (date: Date) => {
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return '刚刚'
    if (diffInMinutes < 60) return `${diffInMinutes}分钟前`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}小时前`
    return `${Math.floor(diffInMinutes / 1440)}天前`
  }

  // 格式化图表时间标签
  const formatChartTimeLabel = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    })
  }

  // 加载历史数据
  const loadHistoryData = async () => {
    if (!isExpanded || loadingHistory) return

    setLoadingHistory(true)
    try {
      const result = await databaseApi.getAreaMetricRecords(metric.id, 30) // 获取最近30条记录
      if (result.success && result.data) {
        setHistoryRecords(result.data)
      }
    } catch (error) {
      console.error('Failed to load history data:', error)
    } finally {
      setLoadingHistory(false)
    }
  }

  // 当展开状态改变时加载历史数据
  useEffect(() => {
    if (isExpanded) {
      loadHistoryData()
    }
  }, [isExpanded])

  // 切换展开状态
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  // {{ AURA-X: Modify - 使用双向KPI进度计算. Approval: 寸止(ID:1738157400). }}
  // 计算进度百分比，支持增长型和减少型指标
  const getProgress = () => {
    if (!metric.target) return null
    return calculateMetricProgress(metric)
  }

  // 获取指标方向
  const getDirection = () => {
    return getMetricDirection(metric)
  }

  // 获取状态颜色
  const getStatusColor = () => {
    const progress = getProgress()
    if (progress === null) return 'text-muted-foreground'
    if (progress >= 100) return 'text-green-600'
    if (progress >= 75) return 'text-blue-600'
    if (progress >= 50) return 'text-yellow-600'
    return 'text-red-600'
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!value.trim()) {
      addNotification({
        type: 'error',
        title: 'Invalid Input',
        message: 'Please enter a value'
      })
      return
    }

    setIsSubmitting(true)
    try {
      const result = await databaseApi.createAreaMetricRecord({
        metricId: metric.id,
        value: value.trim(),
        note: note.trim() || undefined
      })

      if (result.success) {
        addNotification({
          type: 'success',
          title: 'Record Added',
          message: `New value recorded for ${metric.name}`
        })
        
        if (onRecordCreated) {
          onRecordCreated(result.data)
        }
        
        setIsOpen(false)
        setValue('')
        setNote('')
      } else {
        throw new Error(result.error || 'Failed to create record')
      }
    } catch (error) {
      console.error('Failed to create area metric record:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Record',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleEdit = () => {
    if (onEdit) {
      onEdit(metric)
    }
  }

  const handleDelete = () => {
    if (onDelete) {
      onDelete(metric)
    }
  }

  const progress = getProgress()



  return (
    <div className={cn('space-y-0', className)}>
      <Card className="group relative transition-all hover:shadow-md">
        {/* 可点击的主体部分 */}
        <div
          className="cursor-pointer"
          onClick={toggleExpanded}
        >
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <CardTitle className="text-sm font-medium truncate">{metric.name}</CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <span className={cn('text-lg font-bold', getStatusColor())}>
                    {metric.value}
                  </span>
                  {metric.unit && (
                    <span className="text-xs text-muted-foreground">{metric.unit}</span>
                  )}
                  {metric.target && (
                    <span className="text-xs text-muted-foreground">
                      {/* {{ AURA-X: Add - 显示方向指示器. Approval: 寸止(ID:1738157400). }} */}
                      {getDirection() === 'decrease' ? '→' : '/'} {metric.target}
                    </span>
                  )}
                </div>
              </div>

              {/* 操作按钮区域 */}
              <div className="flex items-center gap-1" onClick={(e) => e.stopPropagation()}>
                <Dialog open={isOpen} onOpenChange={setIsOpen}>
                  <DialogTrigger asChild>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Update {metric.name}</DialogTitle>
                      <DialogDescription>
                        Record a new value for this metric
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="value">New Value</Label>
                        <Input
                          id="value"
                          type="number"
                          value={value}
                          onChange={(e) => setValue(e.target.value)}
                          placeholder={`Current: ${metric.value}`}
                        />
                      </div>
                      <div>
                        <Label htmlFor="note">Note (optional)</Label>
                        <Textarea
                          id="note"
                          value={note}
                          onChange={(e) => setNote(e.target.value)}
                          placeholder="Add a note about this update..."
                          rows={3}
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleSubmit} disabled={isSubmitting}>
                        {isSubmitting ? 'Updating...' : 'Update'}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={handleEdit}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Metric
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleDelete} className="text-destructive">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Metric
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* 展开/收起指示器 */}
                <Button
                  size="sm"
                  variant="ghost"
                  className="ml-1"
                >
                  {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </CardHeader>

          <CardContent className="pt-0">
            {/* Progress Bar */}
            {progress !== null && (
              <div className="mb-3">
                <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
                  <span>Progress</span>
                  <span>{progress.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className={cn(
                      'h-2 rounded-full transition-all',
                      progress >= 100 ? 'bg-green-500' :
                      progress >= 75 ? 'bg-blue-500' :
                      progress >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                    )}
                    style={{ width: `${Math.min(progress, 100)}%` }}
                  />
                </div>
              </div>
            )}

            {/* 最后更新时间 */}
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Calendar className="h-3 w-3" />
              <span>最后更新于: {formatRelativeTime(new Date(metric.updatedAt))}</span>
            </div>
          </CardContent>
        </div>

        {/* 展开的历史数据区域 */}
        {isExpanded && (
          <CardContent className="pt-0 border-t">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                <span className="font-medium text-sm">历史趋势</span>
              </div>

              {loadingHistory ? (
                <div className="text-center py-4 text-muted-foreground">
                  加载历史数据中...
                </div>
              ) : historyRecords.length > 0 ? (
                <div className="space-y-2">
                  {/* 折线图表 */}
                  <div className="h-32 bg-muted/30 rounded-lg p-3 relative">
                    {(() => {
                      const data = historyRecords.slice(-10).reverse() // 最近10条，时间正序
                      if (data.length < 2) {
                        return (
                          <div className="flex items-center justify-center h-full text-muted-foreground text-sm">
                            需要至少2个数据点才能显示趋势
                          </div>
                        )
                      }

                      const values = data.map(r => parseFloat(r.value))
                      const minValue = Math.min(...values)
                      const maxValue = Math.max(...values)
                      const valueRange = maxValue - minValue || 1 // 避免除零

                      const width = 280 // 图表宽度
                      const height = 100 // 图表高度
                      const padding = 20

                      // 计算点的坐标
                      const points = data.map((record, index) => {
                        const x = padding + (index / (data.length - 1)) * (width - 2 * padding)
                        const value = parseFloat(record.value)
                        const y = height - padding - ((value - minValue) / valueRange) * (height - 2 * padding)
                        return { x, y, value, record }
                      })

                      // 生成路径字符串
                      const pathData = points.map((point, index) =>
                        `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
                      ).join(' ')

                      return (
                        <svg width={width} height={height} className="w-full h-full">
                          {/* 网格线 */}
                          <defs>
                            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="currentColor" strokeWidth="0.5" opacity="0.1"/>
                            </pattern>
                          </defs>
                          <rect width="100%" height="100%" fill="url(#grid)" />

                          {/* 折线 */}
                          <path
                            d={pathData}
                            fill="none"
                            stroke="hsl(var(--primary))"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />

                          {/* 数据点和标签 */}
                          {points.map((point, index) => (
                            <g key={index}>
                              {/* 数据点 */}
                              <circle
                                cx={point.x}
                                cy={point.y}
                                r="3"
                                fill="hsl(var(--primary))"
                                stroke="white"
                                strokeWidth="2"
                              />

                              {/* 数值标签 */}
                              <text
                                x={point.x}
                                y={point.y - 8}
                                textAnchor="middle"
                                className="text-xs fill-current"
                                style={{ fontSize: '10px' }}
                              >
                                {point.value}{metric.unit}
                              </text>

                              {/* 时间标签 */}
                              <text
                                x={point.x}
                                y={height - 5}
                                textAnchor="middle"
                                className="text-xs fill-muted-foreground"
                                style={{ fontSize: '9px' }}
                              >
                                {formatChartTimeLabel(new Date(point.record.recordedAt))}
                              </text>
                            </g>
                          ))}
                        </svg>
                      )
                    })()}
                  </div>

                  {/* 最近记录列表 */}
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {historyRecords.slice(0, 5).map((record) => (
                      <div key={record.id} className="flex items-center justify-between text-xs">
                        <span className="font-medium">{record.value} {metric.unit}</span>
                        <span className="text-muted-foreground">
                          {formatRelativeTime(new Date(record.recordedAt))}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  暂无历史数据
                </div>
              )}
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  )
}

export default AreaMetricQuickInput
