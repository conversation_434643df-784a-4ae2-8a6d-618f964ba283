import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Label } from '../ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Badge } from '../ui/badge'
import { cn } from '../../lib/utils'
import { useAreaStore } from '../../store/areaStore'
import { useLanguage } from '../../contexts/LanguageContext'
import type { Project } from '../../../../shared/types'

interface CreateProjectDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => void
  initialData?: Partial<Project>
}

export function CreateProjectDialog({
  isOpen,
  onClose,
  onSubmit,
  initialData
}: CreateProjectDialogProps) {
  const { areas } = useAreaStore()
  const { t } = useLanguage()
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    description: initialData?.description || '',
    goal: initialData?.goal || '',
    deliverable: initialData?.deliverable || '',
    status: initialData?.status || 'Not Started',
    progress: initialData?.progress || 0,
    startDate: initialData?.startDate
      ? new Date(initialData.startDate).toISOString().split('T')[0]
      : '',
    deadline: initialData?.deadline
      ? new Date(initialData.deadline).toISOString().split('T')[0]
      : '',
    areaId: initialData?.areaId || 'none',
    archived: initialData?.archived || false
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const statusOptions = [
    { value: 'Not Started', label: t('components.projectCard.status.notStarted'), color: 'bg-gray-100 text-gray-800' },
    { value: 'In Progress', label: t('components.projectCard.status.inProgress'), color: 'bg-blue-100 text-blue-800' },
    { value: 'At Risk', label: t('components.projectCard.status.atRisk'), color: 'bg-yellow-100 text-yellow-800' },
    { value: 'Paused', label: t('components.projectCard.status.paused'), color: 'bg-gray-100 text-gray-800' }
  ]

  const deliverableTypes = [
    {
      value: 'document',
      label: t('components.createProjectDialog.deliverableTypes.document'),
      example: t('components.createProjectDialog.deliverableTypes.documentExample')
    },
    {
      value: 'metric',
      label: t('components.createProjectDialog.deliverableTypes.metric'),
      example: t('components.createProjectDialog.deliverableTypes.metricExample')
    },
    {
      value: 'checklist',
      label: t('components.createProjectDialog.deliverableTypes.checklist'),
      example: t('components.createProjectDialog.deliverableTypes.checklistExample')
    },
    {
      value: 'other',
      label: t('components.createProjectDialog.deliverableTypes.other'),
      example: t('components.createProjectDialog.deliverableTypes.otherExample')
    }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name.trim()) return

    setIsSubmitting(true)
    try {
      const projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'> = {
        name: formData.name.trim(),
        description: formData.description.trim() || null,
        goal: formData.goal.trim() || null,
        deliverable: formData.deliverable.trim() || null,
        status: formData.status,
        progress: formData.progress,
        startDate: formData.startDate ? new Date(formData.startDate) : null,
        deadline: formData.deadline ? new Date(formData.deadline) : null,
        areaId: formData.areaId === 'none' ? null : formData.areaId,
        archived: formData.archived
      }

      await onSubmit(projectData)
      onClose()

      // Reset form
      setFormData({
        name: '',
        description: '',
        goal: '',
        deliverable: '',
        status: 'Not Started',
        progress: 0,
        startDate: '',
        deadline: '',
        areaId: 'none',
        archived: false
      })
    } catch (error) {
      console.error('Failed to create project:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-lg bg-project/10 flex items-center justify-center">
              <span className="text-project font-semibold">P</span>
            </div>
            {initialData ? t('components.createProjectDialog.title.edit') : t('components.createProjectDialog.title.create')}
          </DialogTitle>
          <DialogDescription>
            {t('components.createProjectDialog.description')}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t('components.createProjectDialog.fields.name')} *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                placeholder={t('components.createProjectDialog.fields.namePlaceholder')}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">{t('components.createProjectDialog.fields.description')}</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                placeholder={t('components.createProjectDialog.fields.descriptionPlaceholder')}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="goal">{t('components.createProjectDialog.fields.goal')}</Label>
              <Textarea
                id="goal"
                value={formData.goal}
                onChange={(e) => setFormData((prev) => ({ ...prev, goal: e.target.value }))}
                placeholder={t('components.createProjectDialog.fields.goalPlaceholder')}
                rows={2}
              />
            </div>
          </div>

          {/* Deliverable */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="deliverable">{t('components.createProjectDialog.fields.deliverable')}</Label>
              <Textarea
                id="deliverable"
                value={formData.deliverable}
                onChange={(e) => setFormData((prev) => ({ ...prev, deliverable: e.target.value }))}
                placeholder={t('components.createProjectDialog.fields.deliverablePlaceholder')}
                rows={2}
              />
              <div className="text-xs text-muted-foreground">
                <p className="mb-2">{t('components.createProjectDialog.deliverableTypes.examples')}</p>
                <div className="space-y-1">
                  {deliverableTypes.map((type) => (
                    <div key={type.value} className="flex items-center gap-2">
                      <span>{type.label}</span>
                      <span className="text-muted-foreground/70">{type.example}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Status and Progress */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">{t('components.createProjectDialog.fields.status')}</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={cn('text-xs', option.color)}>
                          {option.label}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="progress">{t('components.createProjectDialog.fields.progress')} (%)</Label>
              <Input
                id="progress"
                type="number"
                min="0"
                max="100"
                value={formData.progress}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, progress: parseInt(e.target.value) || 0 }))
                }
              />
            </div>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">{t('components.createProjectDialog.fields.startDate')}</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData((prev) => ({ ...prev, startDate: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="deadline">{t('components.createProjectDialog.fields.deadline')}</Label>
              <Input
                id="deadline"
                type="date"
                value={formData.deadline}
                onChange={(e) => setFormData((prev) => ({ ...prev, deadline: e.target.value }))}
              />
            </div>
          </div>

          {/* Area Association */}
          <div className="space-y-2">
            <Label htmlFor="area">{t('components.createProjectDialog.fields.area')}</Label>
            <Select
              value={formData.areaId}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, areaId: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('components.createProjectDialog.fields.selectArea')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">{t('components.createProjectDialog.fields.noArea')}</SelectItem>
                {areas
                  .filter((area) => !area.archived)
                  .map((area) => (
                    <SelectItem key={area.id} value={area.id}>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-area"></div>
                        {area.name}
                      </div>
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
              {t('components.createProjectDialog.buttons.cancel')}
            </Button>
            <Button type="submit" disabled={!formData.name.trim() || isSubmitting}>
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  <span>{initialData ? t('components.createProjectDialog.buttons.updating') : t('components.createProjectDialog.buttons.creating')}</span>
                </div>
              ) : initialData ? (
                t('components.createProjectDialog.buttons.update')
              ) : (
                t('components.createProjectDialog.buttons.create')
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default CreateProjectDialog
