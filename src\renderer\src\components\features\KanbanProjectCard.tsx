import { <PERSON> } from 'react-router-dom'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card'

import { Button } from '../ui/button'
import { Progress } from '../ui/progress'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { cn } from '../../lib/utils'
import { useLanguage } from '../../contexts/LanguageContext'
import type { Project } from '../../../../shared/types'

interface KanbanProjectCardProps {
  project: Project
  onEdit?: (project: Project) => void
  onDelete?: (project: Project) => void
  onArchive?: (project: Project) => void
  className?: string
}

export function KanbanProjectCard({
  project,
  onEdit,
  onDelete,
  onArchive,
  className
}: KanbanProjectCardProps) {
  const { t } = useLanguage()

  // Calculate days until deadline
  const getDaysUntilDeadline = () => {
    if (!project.deadline) return null
    const now = new Date()
    const deadline = new Date(project.deadline)
    const diffTime = deadline.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const daysUntil = getDaysUntilDeadline()

  const getDeadlineColor = (days: number | null) => {
    if (days === null) return 'text-muted-foreground'
    if (days < 0) return 'text-red-600'
    if (days <= 3) return 'text-yellow-600'
    if (days <= 7) return 'text-blue-600'
    return 'text-muted-foreground'
  }

  const formatDeadline = (days: number | null) => {
    if (days === null) return 'No deadline'
    if (days < 0) return `${Math.abs(days)}天前到期`
    if (days === 0) return '今天到期'
    if (days === 1) return '明天到期'
    return `${days}天后到期`
  }

  const getPriorityColor = (progress: number) => {
    if (progress >= 80) return 'text-green-600'
    if (progress >= 50) return 'text-blue-600'
    if (progress >= 20) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <Card
      className={cn(
        'group hover:shadow-md transition-all duration-200 cursor-pointer',
        project.status === 'Completed' && 'opacity-75',
        className
      )}
    >
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-sm font-medium truncate mb-1">
              <Link to={`/projects/${project.id}`} className="hover:text-primary transition-colors">
                {project.name}
              </Link>
            </CardTitle>
            {project.description && (
              <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                {project.description}
              </p>
            )}
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 5v.01M12 12v.01M12 19v.01"
                  />
                </svg>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit?.(project)}>
                {t('components.projectCard.actions.editProject')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onArchive?.(project)}>
                {t('components.projectCard.actions.archiveProject')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete?.(project)}
                className="text-red-600 focus:text-red-600"
              >
                {t('components.projectCard.actions.deleteProject')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0 space-y-3">
        {/* Progress */}
        <div className="space-y-1">
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">进度</span>
            <span className={cn('font-medium', getPriorityColor(project.progress))}>
              {project.progress}%
            </span>
          </div>
          <Progress value={project.progress} className="h-1.5" />
        </div>

        {/* Goal - Only show if exists and keep it short */}
        {project.goal && (
          <div className="space-y-1">
            <div className="text-xs font-medium text-muted-foreground">目标</div>
            <p className="text-xs line-clamp-2">{project.goal}</p>
          </div>
        )}

        {/* Deadline */}
        {project.deadline && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">截止日期</span>
            <span className={getDeadlineColor(daysUntil)}>{formatDeadline(daysUntil)}</span>
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="text-xs text-muted-foreground">
            {new Date(project.updatedAt).toLocaleDateString()}
          </div>
          <Button asChild variant="ghost" size="sm" className="h-6 px-2 text-xs">
            <Link to={`/projects/${project.id}`}>详情</Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default KanbanProjectCard
