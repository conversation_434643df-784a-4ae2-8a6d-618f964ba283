import { <PERSON> } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Progress } from '../ui/progress'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { cn } from '../../lib/utils'
import { useLanguage } from '../../contexts/LanguageContext'
import type { Project } from '../../../../shared/types'

interface ProjectCardProps {
  project: Project
  onEdit?: (project: Project) => void
  onDelete?: (project: Project) => void
  onArchive?: (project: Project) => void
  className?: string
  // {{ AURA-X: Add - 添加来源信息用于动态返回. Approval: 寸止(ID:1738157400). }}
  fromArea?: {
    areaId: string
    areaName: string
  }
}

export function ProjectCard({ project, onEdit, onDelete, onArchive, className, fromArea }: ProjectCardProps) {
  const { t } = useLanguage()

  // Calculate days until deadline
  const getDaysUntilDeadline = () => {
    if (!project.deadline) return null
    const now = new Date()
    const deadline = new Date(project.deadline)
    const diffTime = deadline.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const daysUntil = getDaysUntilDeadline()

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Not Started':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'Completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'In Progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'At Risk':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'Paused':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'Not Started':
        return t('components.projectCard.status.notStarted')
      case 'Completed':
        return t('components.projectCard.status.completed')
      case 'In Progress':
        return t('components.projectCard.status.inProgress')
      case 'At Risk':
        return t('components.projectCard.status.atRisk')
      case 'Paused':
        return t('components.projectCard.status.paused')
      default:
        return status
    }
  }

  const getDeadlineColor = (days: number | null) => {
    if (days === null) return 'text-muted-foreground'
    if (days < 0) return 'text-red-600'
    if (days <= 3) return 'text-yellow-600'
    if (days <= 7) return 'text-blue-600'
    return 'text-muted-foreground'
  }

  const formatDeadline = (days: number | null) => {
    if (days === null) return t('components.projectCard.deadline.noDeadline')
    if (days < 0) return t('components.projectCard.deadline.daysOverdue', { days: Math.abs(days) })
    if (days === 0) return t('components.projectCard.deadline.dueToday')
    if (days === 1) return t('components.projectCard.deadline.dueTomorrow')
    return t('components.projectCard.deadline.daysLeft', { days })
  }

  return (
    <Card
      className={cn(
        'group hover:shadow-md transition-all duration-200 border-l-4 border-project',
        project.status === 'Completed' && 'opacity-75',
        className
      )}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <CardTitle className="text-lg truncate">
                <Link
                  to={`/projects/${project.id}`}
                  state={fromArea ? { from: 'area', areaId: fromArea.areaId, areaName: fromArea.areaName } : undefined}
                  className="hover:text-primary transition-colors"
                >
                  {project.name}
                </Link>
              </CardTitle>
              <Badge variant="outline" className={cn('text-xs', getStatusColor(project.status))}>
                {getStatusText(project.status)}
              </Badge>
            </div>
            {project.description && (
              <CardDescription className="line-clamp-2">{project.description}</CardDescription>
            )}
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 5v.01M12 12v.01M12 19v.01"
                  />
                </svg>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit?.(project)}>
                {t('components.projectCard.actions.editProject')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onArchive?.(project)}>
                {t('components.projectCard.actions.archiveProject')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete?.(project)}
                className="text-red-600 focus:text-red-600"
              >
                {t('components.projectCard.actions.deleteProject')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">{t('components.projectCard.labels.progress')}</span>
            <span className="font-medium">{project.progress}%</span>
          </div>
          <Progress value={project.progress} className="h-2" />
        </div>

        {/* Goal */}
        {project.goal && (
          <div className="space-y-1">
            <div className="text-xs font-medium text-muted-foreground">
              {t('components.projectCard.labels.goal')}
            </div>
            <p className="text-sm line-clamp-2">{project.goal}</p>
          </div>
        )}

        {/* Deliverable */}
        {project.deliverable && (
          <div className="space-y-1">
            <div className="text-xs font-medium text-muted-foreground">
              {t('components.projectCard.labels.deliverable')}
            </div>
            <p className="text-sm line-clamp-2">{project.deliverable}</p>
          </div>
        )}

        {/* Dates */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div>
            {project.startDate && (
              <span>
                {t('components.projectCard.labels.started')} {new Date(project.startDate).toLocaleDateString()}
              </span>
            )}
          </div>
          <div className={getDeadlineColor(daysUntil)}>{formatDeadline(daysUntil)}</div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="text-xs text-muted-foreground">
            {t('components.projectCard.labels.updated')} {new Date(project.updatedAt).toLocaleDateString()}
          </div>
          <Button asChild variant="ghost" size="sm" className="h-7 px-2 text-xs">
            <Link
              to={`/projects/${project.id}`}
              state={fromArea ? { from: 'area', areaId: fromArea.areaId, areaName: fromArea.areaName } : undefined}
            >
              {t('components.projectCard.labels.viewDetails')}
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default ProjectCard
