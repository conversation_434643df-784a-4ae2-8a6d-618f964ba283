import { useState, useMemo, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, Link, useNavigate, useLocation } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Progress } from '../ui/progress'
import { Textarea } from '../ui/textarea'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { Search, Plus } from 'lucide-react'
import { cn } from '../../lib/utils'
import { useProjectStore } from '../../store/projectStore'
import { useTaskStore } from '../../store/taskStore'
import { useAreaStore } from '../../store/areaStore'
import { useUIStore } from '../../store/uiStore'
import { useConfirmDialog } from '../shared/ConfirmDialog'
import CreateProjectDialog from './CreateProjectDialog'
import TaskList from './TaskList'
import CreateTaskDialog from './CreateTaskDialog'
import { DeleteTaskDialog } from './DeleteTaskDialog'
import TaskDetailPanel from './TaskDetailPanel'
// 使用最终版本的KPI管理组件
import ProjectKPIManagement from './ProjectKPIManagement'
import ProjectResources from './ProjectResources'
import ProjectDeliverables from './ProjectDeliverables'
import TaskSearchAndFilter from './TaskSearchAndFilter'
import ResourceLinkDialog from './ResourceLinkDialog'
import type { Project } from '../../../../shared/types'
import type { ExtendedTask } from '../../store/taskStore'

export function ProjectDetailPage() {
  const { projectId } = useParams<{ projectId: string }>()
  const navigate = useNavigate()
  const location = useLocation()
  const isArchived = location.state?.archived || false
  const { projects, updateProject, deleteProject, archiveProject } = useProjectStore()
  const { tasks, createTask, updateTask, deleteTask, moveTask, getDescendantCount, getTaskChildren } = useTaskStore()
  const { areas } = useAreaStore()
  const { addNotification } = useUIStore()
  const { confirm, ConfirmDialog } = useConfirmDialog()

  // {{ AURA-X: Add - 动态返回逻辑. Approval: 寸止(ID:1738157400). }}
  const getBackPath = () => {
    // 检查是否从领域详情页进入
    if (location.state?.from === 'area') {
      return `/areas/${location.state.areaId}`
    }
    // 默认返回项目页面
    return '/projects'
  }

  const getBackText = () => {
    if (location.state?.from === 'area') {
      return `← Back to ${location.state.areaName || 'Area'}`
    }
    return '← Back to Projects'
  }

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isCreateTaskDialogOpen, setIsCreateTaskDialogOpen] = useState(false)
  const [isDeleteTaskDialogOpen, setIsDeleteTaskDialogOpen] = useState(false)
  const [editingTask, setEditingTask] = useState<ExtendedTask | null>(null)
  const [deletingTask, setDeletingTask] = useState<ExtendedTask | null>(null)
  const [parentTaskId, setParentTaskId] = useState<string | undefined>()
  const [notes, setNotes] = useState('')
  const [isNotesEditing, setIsNotesEditing] = useState(false)
  const [isResourceLinkDialogOpen, setIsResourceLinkDialogOpen] = useState(false)
  const [filteredTasks, setFilteredTasks] = useState<ExtendedTask[]>([])
  const [showTaskFilters, setShowTaskFilters] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // {{ AURA-X: Add - 页面加载时滚动到顶部. Approval: 寸止(ID:1738157400). }}
  useEffect(() => {
    const scrollToTop = () => {
      // 找到真正的滚动容器 - Layout 中的主内容滚动容器
      const scrollContainer = document.querySelector('main .overflow-y-auto')
      if (scrollContainer) {
        scrollContainer.scrollTop = 0
      }

      // 备用方案：重置其他可能的滚动容器
      window.scrollTo({ top: 0, left: 0, behavior: 'instant' })
      document.documentElement.scrollTop = 0
      document.body.scrollTop = 0
    }

    // 立即执行
    scrollToTop()

    // 延迟执行，确保 DOM 完全更新
    const timer1 = setTimeout(scrollToTop, 0)
    const timer2 = setTimeout(scrollToTop, 100)

    return () => {
      clearTimeout(timer1)
      clearTimeout(timer2)
    }
  }, [projectId])

  // TaskDetailPanel 状态
  const [selectedTaskForDetail, setSelectedTaskForDetail] = useState<ExtendedTask | null>(null)
  const [isTaskDetailPanelOpen, setIsTaskDetailPanelOpen] = useState(false)

  const project = projects.find((p) => p.id === projectId)
  const projectTasks = tasks.filter((task) => task.projectId === projectId)
  const associatedArea = project?.areaId ? areas.find((a) => a.id === project.areaId) : null

  // 初始化过滤后的任务列表
  useEffect(() => {
    if (filteredTasks.length === 0 && projectTasks.length > 0) {
      setFilteredTasks(projectTasks)
    }
  }, [projectTasks, filteredTasks.length])

  if (!project) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <div className="text-4xl mb-4">📋</div>
          <h2 className="text-xl font-semibold mb-2">Project Not Found</h2>
          <p className="text-muted-foreground mb-4">
            The project you're looking for doesn't exist or has been deleted.
          </p>
          <Button asChild>
            <Link to="/projects">Back to Projects</Link>
          </Button>
        </div>
      </div>
    )
  }

  // Calculate project statistics
  const stats = useMemo(() => {
    const totalTasks = projectTasks.length
    const completedTasks = projectTasks.filter((task) => task.completed).length
    const overdueTasks = projectTasks.filter((task) => {
      if (!task.deadline || task.completed) return false
      return new Date(task.deadline) < new Date()
    }).length
    const upcomingTasks = projectTasks.filter((task) => {
      if (!task.deadline || task.completed) return false
      const deadline = new Date(task.deadline)
      const now = new Date()
      const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
      return deadline >= now && deadline <= sevenDaysFromNow
    }).length

    return {
      totalTasks,
      completedTasks,
      overdueTasks,
      upcomingTasks,
      progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
    }
  }, [projectTasks])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'In Progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'At Risk':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'Paused':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getDaysUntilDeadline = () => {
    if (!project.deadline) return null
    const now = new Date()
    const deadline = new Date(project.deadline)
    const diffTime = deadline.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const daysUntil = getDaysUntilDeadline()

  // Function to update project progress based on task completion
  const updateProjectProgress = () => {
    // Use a small delay to ensure task state is updated first
    setTimeout(() => {
      const { tasks: latestTasks } = useTaskStore.getState()
      const currentProjectTasks = latestTasks.filter((task) => task.projectId === project.id)
      const completedTasks = currentProjectTasks.filter((task) => task.completed)
      const progressPercentage =
        currentProjectTasks.length > 0
          ? Math.round((completedTasks.length / currentProjectTasks.length) * 100)
          : 0

      console.log('🔄 Updating project progress:', {
        projectId: project.id,
        totalTasks: currentProjectTasks.length,
        completedTasks: completedTasks.length,
        oldProgress: project.progress,
        newProgress: progressPercentage
      })

      // Always update to ensure synchronization
      updateProject(project.id, {
        progress: progressPercentage,
        updatedAt: new Date()
      })
    }, 10) // Small delay to ensure task state is updated
  }

  const handleEditProject = async (
    projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>
  ) => {
    updateProject(project.id, {
      ...projectData,
      updatedAt: new Date()
    })
    setIsEditDialogOpen(false)
  }

  const handleDeleteProject = () => {
    confirm({
      title: '删除项目',
      description: `确定要删除"${project.name}"吗？此操作无法撤销。`,
      variant: 'destructive',
      confirmText: '删除',
      cancelText: '取消',
      onConfirm: () => {
        deleteProject(project.id)
        navigate(getBackPath())
      }
    })
  }

  const handleArchiveProject = () => {
    confirm({
      title: '归档项目',
      description: `归档"${project.name}"？您可以稍后从归档中恢复。`,
      variant: 'warning',
      confirmText: '归档',
      cancelText: '取消',
      onConfirm: async () => {
        await archiveProject(project.id)
        navigate(getBackPath())
      }
    })
  }

  const handleSaveNotes = () => {
    // In a real implementation, this would save to a notes field or separate notes system
    setIsNotesEditing(false)
  }

  const handleCreateTask = async (
    taskData: Omit<ExtendedTask, 'id' | 'createdAt' | 'updatedAt'>
  ) => {
    console.log('📝 Creating task with data:', {
      content: taskData.content,
      parentId: taskData.parentId,
      projectId: project.id
    })

    try {
      // Ensure projectId is set
      const taskWithProject = {
        ...taskData,
        projectId: project.id
      }

      await createTask(taskWithProject)
      console.log('✅ Task created successfully')

      // Update project progress when task is created
      updateProjectProgress()
    } catch (error) {
      console.error('❌ Failed to create task:', error)
      // Error handling is done in the store
    }
  }

  const handleEditTask = async (taskData: Omit<ExtendedTask, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (editingTask) {
      updateTask(editingTask.id, {
        ...taskData,
        updatedAt: new Date()
      })
      setEditingTask(null)

      // Update project progress when task is updated
      updateProjectProgress()
    }
  }

  // 递归获取所有子任务ID
  const getAllSubtaskIds = (taskId: string): string[] => {
    const children = getTaskChildren(taskId)
    const allIds: string[] = []

    children.forEach(child => {
      allIds.push(child.id)
      // 递归获取子任务的子任务
      allIds.push(...getAllSubtaskIds(child.id))
    })

    return allIds
  }

  // 检查任务是否有子任务
  const hasSubtasks = (taskId: string): boolean => {
    return getTaskChildren(taskId).length > 0
  }

  // 批量更新任务状态
  const updateTasksStatus = async (taskIds: string[], completed: boolean) => {
    try {
      // 使用Promise.all并行更新所有任务
      await Promise.all(
        taskIds.map(async (id) => {
          const updates: any = { completed }

          // 如果标记为完成，同时更新相关字段
          if (completed) {
            updates.status = 'done'
            updates.completedAt = new Date()
          } else {
            // 如果取消完成，重置相关字段
            updates.status = 'todo'
            updates.completedAt = null
          }

          updateTask(id, updates)
        })
      )

      // 更新项目进度
      updateProjectProgress()

      return true
    } catch (error) {
      console.error('Failed to update tasks status:', error)
      return false
    }
  }

  const handleToggleTask = async (taskId: string, completed: boolean) => {
    const task = tasks.find(t => t.id === taskId)
    if (!task) return

    // 如果是叶子任务（没有子任务），直接切换状态
    if (!hasSubtasks(taskId)) {
      updateTask(taskId, {
        completed,
        status: completed ? 'done' : 'todo',
        completedAt: completed ? new Date() : null
      })
      updateProjectProgress()
      return
    }

    // 如果是父任务且要标记为完成，显示确认对话框
    if (completed) {
      const allSubtaskIds = getAllSubtaskIds(taskId)
      const totalTasks = allSubtaskIds.length + 1 // 包含父任务本身

      confirm({
        title: '批量完成任务',
        description: `是否将此任务及其所有 ${allSubtaskIds.length} 个子任务都标记为完成状态？`,
        variant: 'default',
        confirmText: '是',
        cancelText: '否',
        onConfirm: async () => {
          setIsSubmitting(true)
          try {
            // 更新父任务和所有子任务
            const allTaskIds = [taskId, ...allSubtaskIds]
            const success = await updateTasksStatus(allTaskIds, true)

            if (success) {
              addNotification({
                type: 'success',
                title: '任务已完成',
                message: `成功完成 ${totalTasks} 个任务`
              })
            } else {
              addNotification({
                type: 'error',
                title: '更新失败',
                message: '部分任务状态更新失败，请重试'
              })
            }
          } catch (error) {
            console.error('Failed to complete tasks:', error)
            addNotification({
              type: 'error',
              title: '操作失败',
              message: '任务状态更新失败，请重试'
            })
          } finally {
            setIsSubmitting(false)
          }
        },
        onCancel: () => {
          // 用户取消操作，不做任何处理
          console.log('User cancelled task completion')
        }
      })
    } else {
      // 如果是取消完成状态，直接处理（不需要确认）
      updateTask(taskId, {
        completed: false,
        status: 'todo',
        completedAt: null
      })
      updateProjectProgress()
    }
  }

  const handleDeleteTask = (taskId: string) => {
    const task = tasks.find((t) => t.id === taskId)
    if (task) {
      setDeletingTask(task)
      setIsDeleteTaskDialogOpen(true)
    }
  }

  const handleConfirmDeleteTask = () => {
    if (deletingTask) {
      deleteTask(deletingTask.id)
      setDeletingTask(null)

      // Update project progress when task is deleted
      updateProjectProgress()
    }
  }

  const handleAddSubtask = (parentId: string) => {
    setParentTaskId(parentId)
    setIsCreateTaskDialogOpen(true)
  }

  const handleTaskMove = (taskId: string, newParentId?: string, newIndex?: number) => {
    console.log(`🎯 ProjectDetailPage - handleTaskMove called:`, { taskId, newParentId, newIndex })
    moveTask(taskId, newParentId, newIndex)

    // Update project progress after task move (in case task hierarchy affects completion calculation)
    updateProjectProgress()
  }

  // TaskDetailPanel 处理函数
  const handleTaskDetailSave = async (taskData: Partial<ExtendedTask>) => {
    if (selectedTaskForDetail) {
      updateTask(selectedTaskForDetail.id, {
        ...taskData,
        updatedAt: new Date()
      })

      // 更新项目进度
      updateProjectProgress()

      // 关闭面板
      setIsTaskDetailPanelOpen(false)
      setSelectedTaskForDetail(null)
    }
  }

  const handleTaskDetailClose = () => {
    setIsTaskDetailPanelOpen(false)
    setSelectedTaskForDetail(null)
  }

  const handleTaskDetailDelete = (taskId: string) => {
    const task = tasks.find((t) => t.id === taskId)
    if (task) {
      setDeletingTask(task)
      setIsDeleteTaskDialogOpen(true)
      // 关闭详情面板
      setIsTaskDetailPanelOpen(false)
      setSelectedTaskForDetail(null)
    }
  }

  const handleTaskDetailAddSubtask = (parentId: string) => {
    setParentTaskId(parentId)
    setIsCreateTaskDialogOpen(true)
    // 保持详情面板打开状态
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* {{ AURA-X: Modify - 使用动态返回逻辑. Approval: 寸止(ID:1738157400). }} */}
      {/* 返回按钮 */}
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="sm" asChild>
          <Link to={getBackPath()} className="text-muted-foreground hover:text-foreground">
            {getBackText()}
          </Link>
        </Button>
      </div>

      {/* 归档横幅提示 */}
      {isArchived && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-medium text-amber-800">
                您正在查看一个已归档的项目
              </h3>
              <p className="text-sm text-amber-700 mt-1">
                此项目处于只读状态，您可以查看所有历史信息，但无法进行编辑操作。
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 顶部主信息卡片 - 全宽度 */}
      <Card>
        <CardContent className="pt-6">
          {/* 三列式垂直排布 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* 第一列：项目基本信息 */}
            <div className="space-y-4">
              {/* 项目名称和状态 */}
              <div className="space-y-2">
                <div className="flex items-center gap-3">
                  <h1 className="text-2xl font-bold">{project.name}</h1>
                  {/* 状态显示 - 归档状态下只读 */}
                  {isArchived ? (
                    <Badge
                      variant="outline"
                      className={cn('text-sm', getStatusColor(project.status))}
                    >
                      {project.status} (Archived)
                    </Badge>
                  ) : (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Badge
                          variant="outline"
                          className={cn('text-sm cursor-pointer hover:bg-muted', getStatusColor(project.status))}
                        >
                          {project.status}
                        </Badge>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="start">
                        <DropdownMenuItem onClick={() => updateProject(project.id, { status: 'Planning' })}>
                          Planning
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => updateProject(project.id, { status: 'In Progress' })}>
                          In Progress
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => updateProject(project.id, { status: 'At Risk' })}>
                          At Risk
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => updateProject(project.id, { status: 'Paused' })}>
                          Paused
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => updateProject(project.id, { status: 'Completed' })}>
                          Completed
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>

              {/* 项目描述 */}
              {project.description && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm text-muted-foreground">Description</h4>
                  <p className="text-sm">{project.description}</p>
                </div>
              )}

              {/* 项目目标 */}
              {project.goal && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm text-muted-foreground">Project Goal</h4>
                  <p className="text-sm">{project.goal}</p>
                </div>
              )}

              {/* 最终交付物 */}
              {project.deliverable && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm text-muted-foreground">Final Deliverable</h4>
                  <p className="text-sm">{project.deliverable}</p>
                </div>
              )}
            </div>

            {/* 第二列：进度和统计 */}
            <div className="space-y-4">
              {/* 进度圆环 */}
              <div className="flex flex-col items-center space-y-2">
                <div className="text-sm font-medium text-muted-foreground">Overall Progress</div>
                <div className="relative w-20 h-20">
                  <svg className="w-full h-full transform -rotate-90" viewBox="0 0 36 36">
                    {/* 背景圆环 */}
                    <circle
                      cx="18"
                      cy="18"
                      r="15"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="3"
                      className="text-muted-foreground/20"
                    />
                    {/* 进度圆环 */}
                    <circle
                      cx="18"
                      cy="18"
                      r="15"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="3"
                      strokeDasharray={`${2 * Math.PI * 15}`}
                      strokeDashoffset={`${2 * Math.PI * 15 * (1 - Math.max(stats.progressPercentage, 2) / 100)}`}
                      className={cn(
                        'transition-all duration-500',
                        stats.progressPercentage === 0 ? 'text-muted-foreground/40' :
                        stats.progressPercentage >= 100 ? 'text-green-500' :
                        stats.progressPercentage >= 75 ? 'text-blue-500' :
                        stats.progressPercentage >= 50 ? 'text-yellow-500' : 'text-red-500'
                      )}
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-base font-bold">{stats.progressPercentage}%</span>
                  </div>
                </div>
              </div>

              {/* 四个统计数据 */}
              <div className="grid grid-cols-2 gap-3 text-center">
                <div className="space-y-1">
                  <div className="text-xl font-bold text-blue-600">{stats.totalTasks}</div>
                  <div className="text-xs text-muted-foreground">Total Tasks</div>
                </div>
                <div className="space-y-1">
                  <div className="text-xl font-bold text-green-600">{stats.completedTasks}</div>
                  <div className="text-xs text-muted-foreground">Completed</div>
                </div>
                <div className="space-y-1">
                  <div className="text-xl font-bold text-red-600">{stats.overdueTasks}</div>
                  <div className="text-xs text-muted-foreground">Overdue</div>
                </div>
                <div className="space-y-1">
                  <div className="text-xl font-bold text-yellow-600">{stats.upcomingTasks}</div>
                  <div className="text-xs text-muted-foreground">Due Soon</div>
                </div>
              </div>
            </div>

            {/* 第三列：倒计时和日期信息 */}
            <div className="space-y-4">
              {/* 倒计时 */}
              {project.deadline && daysUntil !== null && (
                <div className="text-center p-4 rounded-lg bg-muted/50">
                  <div className="text-sm font-medium text-muted-foreground mb-2">Countdown</div>
                  <div className={cn(
                    "text-3xl font-bold mb-1",
                    daysUntil < 0 ? "text-red-600" :
                    daysUntil <= 7 ? "text-yellow-600" : "text-green-600"
                  )}>
                    {daysUntil < 0 ? Math.abs(daysUntil) : daysUntil}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {daysUntil < 0 ? 'Days Overdue' :
                     daysUntil === 0 ? 'Due Today' :
                     daysUntil === 1 ? 'Day Left' : 'Days Left'}
                  </div>
                </div>
              )}

              {/* 各种日期信息 */}
              <div className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Created</span>
                    <span>{new Date(project.createdAt).toLocaleDateString()}</span>
                  </div>

                  {project.deadline && (
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Deadline</span>
                      <span>{new Date(project.deadline).toLocaleDateString()}</span>
                    </div>
                  )}

                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Updated</span>
                    <span>{new Date(project.updatedAt).toLocaleDateString()}</span>
                  </div>

                  {associatedArea && (
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Area</span>
                      <Link
                        to={`/areas/${associatedArea.id}`}
                        className="text-area hover:underline flex items-center gap-1"
                      >
                        <div className="w-2 h-2 rounded-full bg-area"></div>
                        {associatedArea.name}
                      </Link>
                    </div>
                  )}
                </div>
              </div>

              {/* 操作按钮 - 归档状态下隐藏 */}
              {!isArchived && (
                <div className="pt-4 border-t border-border">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="w-full">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 5v.01M12 12v.01M12 19v.01"
                          />
                        </svg>
                        Actions
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setIsEditDialogOpen(true)}>
                        Edit Project
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={handleArchiveProject}>Archive Project</DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={handleDeleteProject}
                        className="text-red-600 focus:text-red-600"
                      >
                        Delete Project
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 主要内容区域 - 左右布局 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：项目任务 */}
        <div className="lg:col-span-2 space-y-6">
          {/* Tasks */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Project Tasks</CardTitle>
                  <CardDescription>Manage tasks and subtasks for this project</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  {!isArchived && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowTaskFilters(!showTaskFilters)}
                    >
                      <Search className="h-4 w-4 mr-2" />
                      {showTaskFilters ? 'Hide Filters' : 'Show Filters'}
                    </Button>
                  )}
                  {!isArchived && (
                    <Button variant="outline" size="sm" onClick={() => setIsCreateTaskDialogOpen(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Task
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* 搜索和过滤器 */}
              {showTaskFilters && (
                <div className="mb-6">
                  <TaskSearchAndFilter
                    tasks={projectTasks as any[]}
                    onFilteredTasksChange={(filtered) => setFilteredTasks(filtered as ExtendedTask[])}
                    className="mb-4"
                  />
                </div>
              )}

              {projectTasks.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <div className="text-4xl mb-2">📝</div>
                  <p className="text-sm">No tasks yet</p>
                  <p className="text-xs mt-1">Add your first task to get started</p>
                </div>
              ) : (
                <TaskList
                  tasks={showTaskFilters ? filteredTasks : projectTasks}
                  onTaskToggle={isArchived ? undefined : handleToggleTask}
                  onTaskEdit={isArchived ? undefined : setEditingTask}
                  onTaskDelete={isArchived ? undefined : handleDeleteTask}
                  onTaskAddSubtask={isArchived ? undefined : handleAddSubtask}
                  onTaskMove={isArchived ? undefined : handleTaskMove}
                  onTaskClick={(task) => {
                    setSelectedTaskForDetail(task)
                    setIsTaskDetailPanelOpen(true)
                  }}
                  className="space-y-2"
                  useEnhancedView={false}
                  useHierarchicalView={projectTasks.length <= 50}
                  useVirtualizedView={projectTasks.length > 50}
                  showHierarchyControls={!isArchived}
                />
              )}
            </CardContent>
          </Card>
        </div>

        {/* 右侧：其他内容 */}
        <div className="space-y-6">
          {/* KPI Management */}
          <ProjectKPIManagement
            projectId={project.id}
          />

          {/* Project Deliverables */}
          <ProjectDeliverables projectId={project.id} />

          {/* Project Resources */}
          <ProjectResources projectId={project.id} />

          {/* Notes */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Project Notes</CardTitle>
                {!isArchived && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsNotesEditing(!isNotesEditing)}
                  >
                    {isNotesEditing ? 'Cancel' : 'Edit'}
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {isNotesEditing ? (
                <div className="space-y-3">
                  <Textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Add project notes, meeting minutes, or important information..."
                    rows={6}
                  />
                  <div className="flex gap-2">
                    <Button size="sm" onClick={handleSaveNotes}>
                      Save Notes
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => setIsNotesEditing(false)}>
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  {notes || 'No notes yet. Click Edit to add project notes.'}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions - 归档状态下隐藏 */}
          {!isArchived && (
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => setIsCreateTaskDialogOpen(true)}
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                  Add Task
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => setIsResourceLinkDialogOpen(true)}
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                    />
                  </svg>
                  Link Resource
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                  View Analytics
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Edit Project Dialog */}
      <CreateProjectDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        onSubmit={handleEditProject}
        initialData={project}
      />

      {/* Create/Edit Task Dialog */}
      <CreateTaskDialog
        isOpen={isCreateTaskDialogOpen || !!editingTask}
        onClose={() => {
          setIsCreateTaskDialogOpen(false)
          setEditingTask(null)
          setParentTaskId(undefined)
        }}
        onSubmit={editingTask ? handleEditTask : handleCreateTask}
        initialData={editingTask || undefined}
        parentTaskId={parentTaskId}
        projectId={project.id}
      />

      <DeleteTaskDialog
        isOpen={isDeleteTaskDialogOpen}
        onClose={() => {
          setIsDeleteTaskDialogOpen(false)
          setDeletingTask(null)
        }}
        onConfirm={handleConfirmDeleteTask}
        task={deletingTask}
        childrenCount={deletingTask ? getDescendantCount(deletingTask.id) : 0}
      />

      <ResourceLinkDialog
        isOpen={isResourceLinkDialogOpen}
        onClose={() => setIsResourceLinkDialogOpen(false)}
        projectId={project.id}
        onResourcesLinked={() => {
          // Resources will be automatically updated by the ProjectResources component
          setIsResourceLinkDialogOpen(false)
        }}
      />

      {/* Task Detail Panel */}
      <TaskDetailPanel
        task={selectedTaskForDetail}
        isOpen={isTaskDetailPanelOpen}
        onClose={handleTaskDetailClose}
        onSave={isArchived ? () => {} : handleTaskDetailSave}
        onDelete={isArchived ? undefined : handleTaskDetailDelete}
        onAddSubtask={isArchived ? undefined : handleTaskDetailAddSubtask}
        projects={projects}
        areas={areas}
      />

      {/* Confirmation Dialog */}
      <ConfirmDialog />
    </div>
  )
}

export default ProjectDetailPage
