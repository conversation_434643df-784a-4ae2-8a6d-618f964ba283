/**
 * 项目KPI管理组件
 * 专注于核心KPI功能：创建、编辑、数据录入、进度跟踪和基本分析
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Plus, BarChart3, TrendingUp, Target, History, Settings, Link } from 'lucide-react'
import { cn } from '../../lib/utils'
import { useUIStore } from '../../store/uiStore'
import { useLanguage } from '../../contexts/LanguageContext'

// 使用统一的KPI管理架构
import { createProjectKPIManager } from '../../lib/kpiApiAdapters'
import UniversalKPIDialog from '../common/UniversalKPIDialog'
import type { ProjectKPI } from '../../../../shared/types'
import type { CreateKPIData, KPIStatistics } from '../../../../shared/types/kpi'

// 使用新的增强组件
import ProjectKPIQuickInput from './ProjectKPIQuickInput'
import BatchProjectKPIRecordDialog from './BatchProjectKPIRecordDialog'

interface ProjectKPIManagementProps {
  projectId: string
  className?: string
}

export function ProjectKPIManagement({ projectId, className }: ProjectKPIManagementProps) {
  const [kpis, setKpis] = useState<ProjectKPI[]>([])
  const [statistics, setStatistics] = useState<KPIStatistics | null>(null)
  const [loading, setLoading] = useState(true)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showBatchDialog, setShowBatchDialog] = useState(false)
  const [editingKPI, setEditingKPI] = useState<ProjectKPI | null>(null)
  
  const { addNotification } = useUIStore()
  const { t } = useLanguage()
  
  // 使用统一的KPI管理器
  const kpiManager = createProjectKPIManager()

  // 加载KPI数据
  const loadKPIs = async () => {
    try {
      setLoading(true)
      const [kpiData, statsData] = await Promise.all([
        kpiManager.getKPIs(projectId),
        kpiManager.getStatistics(projectId)
      ])
      setKpis(kpiData)
      setStatistics(statsData)
    } catch (error) {
      console.error('Failed to load KPIs:', error)
      addNotification({
        type: 'error',
        title: t('common.error'),
        message: t('pages.projects.detail.projectKPI.loadError')
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadKPIs()
  }, [projectId])

  // 创建KPI
  const handleCreateKPI = async (data: CreateKPIData) => {
    try {
      await kpiManager.create(projectId, data)
      addNotification({
        type: 'success',
        title: t('common.success'),
        message: t('pages.projects.detail.projectKPI.createSuccess')
      })
      loadKPIs()
      setShowCreateDialog(false)
    } catch (error) {
      console.error('Failed to create KPI:', error)
      addNotification({
        type: 'error',
        title: t('common.error'),
        message: t('pages.projects.detail.projectKPI.createError')
      })
    }
  }

  // 更新KPI
  const handleUpdateKPI = async (kpiId: string, data: Partial<CreateKPIData>) => {
    try {
      await kpiManager.update(kpiId, data)
      addNotification({
        type: 'success',
        title: t('common.success'),
        message: t('pages.projects.detail.projectKPI.updateSuccess')
      })
      loadKPIs()
      setEditingKPI(null)
    } catch (error) {
      console.error('Failed to update KPI:', error)
      addNotification({
        type: 'error',
        title: t('common.error'),
        message: t('pages.projects.detail.projectKPI.updateError')
      })
    }
  }

  // 删除KPI
  const handleDeleteKPI = async (kpiId: string) => {
    try {
      await kpiManager.delete(kpiId)
      addNotification({
        type: 'success',
        title: t('common.success'),
        message: t('pages.projects.detail.projectKPI.deleteSuccess')
      })
      loadKPIs()
    } catch (error) {
      console.error('Failed to delete KPI:', error)
      addNotification({
        type: 'error',
        title: t('common.error'),
        message: t('pages.projects.detail.projectKPI.deleteError')
      })
    }
  }

  if (loading) {
    return (
      <div className={cn("space-y-6", className)}>
        {/* 统计概览区域 - 加载状态 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <div className="h-4 bg-muted rounded animate-pulse w-20"></div>
                    <div className="h-8 bg-muted rounded animate-pulse w-16"></div>
                  </div>
                  <div className="h-8 w-8 bg-muted rounded animate-pulse"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* KPI列表加载状态 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Project KPIs
            </CardTitle>
            <CardDescription>
              Manage and track your project key performance indicators
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">{t('common.loading')}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* 统计概览区域 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total KPIs</p>
                <p className="text-2xl font-bold">{statistics?.total || 0}</p>
              </div>
              <Target className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">On Track</p>
                <p className="text-2xl font-bold text-green-600">{statistics?.onTrack || 0}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">At Risk</p>
                <p className="text-2xl font-bold text-yellow-600">{statistics?.atRisk || 0}</p>
              </div>
              <Settings className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Progress</p>
                <p className="text-2xl font-bold">{Math.round(statistics?.averageProgress || 0)}%</p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* KPI列表和操作区域 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                {t('pages.projects.detail.projectKPI.title')}
              </CardTitle>
              <CardDescription>
                {t('pages.projects.detail.projectKPI.description')}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowBatchDialog(true)}
                disabled={kpis.length === 0}
              >
                <History className="h-4 w-4 mr-2" />
                {t('pages.projects.detail.projectKPI.batchRecord')}
              </Button>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                {t('pages.projects.detail.projectKPI.addKPI')}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {kpis.length === 0 ? (
            <div className="text-center py-8">
              <Target className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">{t('pages.projects.detail.projectKPI.noKPIs')}</h3>
              <p className="text-muted-foreground mb-4">{t('pages.projects.detail.projectKPI.noKPIsDescription')}</p>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                {t('pages.projects.detail.projectKPI.createFirst')}
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {kpis.map((kpi) => (
                <ProjectKPIQuickInput
                  key={kpi.id}
                  kpi={kpi}
                  onRecordCreated={() => loadKPIs()}
                  onEdit={(kpi) => setEditingKPI(kpi)}
                  onDelete={(kpi) => handleDeleteKPI(kpi.id)}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>



      {/* 对话框 */}
      <UniversalKPIDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSubmit={handleCreateKPI}
        type="project"
        entityId={projectId}
      />

      <UniversalKPIDialog
        open={!!editingKPI}
        onOpenChange={(open) => !open && setEditingKPI(null)}
        onSubmit={(data) => editingKPI && handleUpdateKPI(editingKPI.id, data)}
        type="project"
        entityId={projectId}
        initialData={editingKPI || undefined}
        mode="edit"
      />

      <BatchProjectKPIRecordDialog
        isOpen={showBatchDialog}
        onClose={() => setShowBatchDialog(false)}
        kpis={kpis}
        onRecordsCreated={loadKPIs}
      />
    </div>
  )
}

export default ProjectKPIManagement
