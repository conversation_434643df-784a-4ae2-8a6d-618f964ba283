// API client for renderer process
import type {
  ElectronApi,
  CreateProjectRequest,
  UpdateProjectRequest,
  CreateAreaRequest,
  CreateTaskRequest,
  UpdateTaskRequest,
  CreateResourceRequest,
  CreateProjectKPIRequest,
  UpdateProjectKPIRequest,
  CreateKPIRecordRequest,
  UpdateKPIRecordRequest,
  CreateAreaMetricRequest,
  UpdateAreaMetricRequest,
  CreateAreaMetricRecordRequest,
  UpdateAreaMetricRecordRequest,
  LinkResourceToProjectRequest,
  ReadFileRequest,
  WriteFileRequest,
  MoveFileRequest,
  CopyFileRequest
} from '../../../shared/ipcTypes'

// Get the API from the global window object
const getElectronAPI = (): ElectronApi => {
  if (!window.electronAPI) {
    throw new Error('Electron API not available. Make sure the preload script is loaded.')
  }
  return window.electronAPI
}

// Database API wrapper with error handling and logging
export const databaseApi = {
  async initialize() {
    const api = getElectronAPI()
    console.log('Initializing database...')
    const result = await api.database.initialize()
    if (result.success) {
      console.log('Database initialized successfully')
    } else {
      console.error('Database initialization failed:', result.error)
    }
    return result
  },

  async testConnection() {
    const api = getElectronAPI()
    console.log('Testing database connection...')
    const result = await api.database.testConnection()
    if (result.success) {
      console.log('Database connection test passed')
    } else {
      console.error('Database connection test failed:', result.error)
    }
    return result
  },

  async createProject(data: CreateProjectRequest) {
    const api = getElectronAPI()
    console.log('Creating project:', data.name)
    const result = await api.database.createProject(data)
    if (result.success) {
      console.log('Project created successfully:', result.data)
    } else {
      console.error('Failed to create project:', result.error)
    }
    return result
  },

  async getProjects() {
    const api = getElectronAPI()
    console.log('Fetching projects...')
    const result = await api.database.getProjects()
    if (result.success) {
      console.log(`Fetched ${result.data?.length || 0} projects`)
    } else {
      console.error('Failed to fetch projects:', result.error)
    }
    return result
  },

  async updateProject(data: UpdateProjectRequest) {
    const api = getElectronAPI()
    console.log('Updating project:', data.id)
    const result = await api.database.updateProject(data)
    if (result.success) {
      console.log('Project updated successfully')
    } else {
      console.error('Failed to update project:', result.error)
    }
    return result
  },

  async deleteProject(id: string) {
    const api = getElectronAPI()
    console.log('Deleting project:', id)
    const result = await api.database.deleteProject(id)
    if (result.success) {
      console.log('Project deleted successfully')
    } else {
      console.error('Failed to delete project:', result.error)
    }
    return result
  },

  async archiveProject(id: string) {
    const api = getElectronAPI()
    console.log('Archiving project:', id)
    const result = await api.database.archiveProject(id)
    if (result.success) {
      console.log('Project archived successfully')
    } else {
      console.error('Failed to archive project:', result.error)
    }
    return result
  },

  async createArea(data: CreateAreaRequest) {
    const api = getElectronAPI()
    console.log('Creating area:', data.name)
    const result = await api.database.createArea(data)
    if (result.success) {
      console.log('Area created successfully:', result.data)
    } else {
      console.error('Failed to create area:', result.error)
    }
    return result
  },

  async getAreas() {
    const api = getElectronAPI()
    console.log('Fetching areas...')
    const result = await api.database.getAreas()
    if (result.success) {
      console.log(`Fetched ${result.data?.length || 0} areas`)
    } else {
      console.error('Failed to fetch areas:', result.error)
    }
    return result
  },

  async deleteArea(id: string) {
    const api = getElectronAPI()
    console.log('Deleting area:', id)
    const result = await api.database.deleteArea(id)
    if (result.success) {
      console.log('Area deleted successfully')
    } else {
      console.error('Failed to delete area:', result.error)
    }
    return result
  },

  async archiveArea(id: string) {
    const api = getElectronAPI()
    console.log('Archiving area:', id)
    const result = await api.database.archiveArea(id)
    if (result.success) {
      console.log('Area archived successfully')
    } else {
      console.error('Failed to archive area:', result.error)
    }
    return result
  },

  async getArchivedProjects() {
    const api = getElectronAPI()
    console.log('Fetching archived projects...')
    const result = await api.database.getArchivedProjects()
    if (result.success) {
      console.log(`Fetched ${result.data?.length || 0} archived projects`)
    } else {
      console.error('Failed to fetch archived projects:', result.error)
    }
    return result
  },

  async getArchivedAreas() {
    const api = getElectronAPI()
    console.log('Fetching archived areas...')
    const result = await api.database.getArchivedAreas()
    if (result.success) {
      console.log(`Fetched ${result.data?.length || 0} archived areas`)
    } else {
      console.error('Failed to fetch archived areas:', result.error)
    }
    return result
  },

  async restoreProject(id: string) {
    const api = getElectronAPI()
    console.log('Restoring project:', id)
    const result = await api.database.restoreProject(id)
    if (result.success) {
      console.log('Project restored successfully')
    } else {
      console.error('Failed to restore project:', result.error)
    }
    return result
  },

  async restoreArea(id: string) {
    const api = getElectronAPI()
    console.log('Restoring area:', id)
    const result = await api.database.restoreArea(id)
    if (result.success) {
      console.log('Area restored successfully')
    } else {
      console.error('Failed to restore area:', result.error)
    }
    return result
  },

  async createTask(data: CreateTaskRequest) {
    const api = getElectronAPI()
    console.log('Creating task:', data.content)
    const result = await api.database.createTask(data)
    if (result.success) {
      console.log('Task created successfully:', result.data)
    } else {
      console.error('Failed to create task:', result.error)
    }
    return result
  },

  async getTasks(filters?: any) {
    const api = getElectronAPI()
    console.log('Fetching tasks with filters:', filters)
    const result = await api.database.getTasks(filters)
    if (result.success) {
      console.log(`Fetched ${result.data?.length || 0} tasks`)
    } else {
      console.error('Failed to fetch tasks:', result.error)
    }
    return result
  },

  async updateTask(data: UpdateTaskRequest) {
    const api = getElectronAPI()
    console.log('Updating task:', data.id)
    const result = await api.database.updateTask(data)
    if (result.success) {
      console.log('Task updated successfully')
    } else {
      console.error('Failed to update task:', result.error)
    }
    return result
  },

  async createResource(data: CreateResourceRequest) {
    const api = getElectronAPI()
    console.log('Creating resource:', data.resourcePath)
    const result = await api.database.createResource(data)
    if (result.success) {
      console.log('Resource created successfully:', result.data)
    } else {
      console.error('Failed to create resource:', result.error)
    }
    return result
  },

  async getResources(filters?: any) {
    const api = getElectronAPI()
    console.log('Fetching resources with filters:', filters)
    const result = await api.database.getResources(filters)
    if (result.success) {
      console.log(`Fetched ${result.data?.length || 0} resources`)
    } else {
      console.error('Failed to fetch resources:', result.error)
    }
    return result
  },

  // ProjectKPI operations
  async createProjectKPI(data: CreateProjectKPIRequest) {
    const api = getElectronAPI()
    console.log('Creating project KPI:', data.name)
    const result = await api.database.createProjectKPI(data)
    if (result.success) {
      console.log('Project KPI created successfully:', result.data)
    } else {
      console.error('Failed to create project KPI:', result.error)
    }
    return result
  },

  async getProjectKPIs(projectId: string) {
    const api = getElectronAPI()
    console.log('Fetching project KPIs for project:', projectId)
    const result = await api.database.getProjectKPIs(projectId)
    if (result.success) {
      console.log(`Fetched ${result.data?.length || 0} project KPIs`)
    } else {
      console.error('Failed to fetch project KPIs:', result.error)
    }
    return result
  },

  async updateProjectKPI(data: UpdateProjectKPIRequest) {
    const api = getElectronAPI()
    console.log('Updating project KPI:', data.id)
    const result = await api.database.updateProjectKPI(data)
    if (result.success) {
      console.log('Project KPI updated successfully')
    } else {
      console.error('Failed to update project KPI:', result.error)
    }
    return result
  },

  async deleteProjectKPI(id: string) {
    const api = getElectronAPI()
    console.log('Deleting project KPI:', id)
    const result = await api.database.deleteProjectKPI(id)
    if (result.success) {
      console.log('Project KPI deleted successfully')
    } else {
      console.error('Failed to delete project KPI:', result.error)
    }
    return result
  },

  // KPI Record operations
  async createKPIRecord(data: CreateKPIRecordRequest) {
    const api = getElectronAPI()
    console.log('Creating KPI record:', data)
    const result = await api.database.createKPIRecord(data)
    if (result.success) {
      console.log('KPI record created successfully:', result.data)
    } else {
      console.error('Failed to create KPI record:', result.error)
    }
    return result
  },

  async getKPIRecords(kpiId: string, limit?: number) {
    const api = getElectronAPI()
    console.log('Fetching KPI records for KPI:', kpiId)
    const result = await api.database.getKPIRecords(kpiId, limit)
    if (result.success) {
      console.log(`Fetched ${result.data?.length || 0} KPI records`)
    } else {
      console.error('Failed to fetch KPI records:', result.error)
    }
    return result
  },

  async updateKPIRecord(data: UpdateKPIRecordRequest) {
    const api = getElectronAPI()
    console.log('Updating KPI record:', data.id)
    const result = await api.database.updateKPIRecord(data)
    if (result.success) {
      console.log('KPI record updated successfully')
    } else {
      console.error('Failed to update KPI record:', result.error)
    }
    return result
  },

  async deleteKPIRecord(id: string) {
    const api = getElectronAPI()
    console.log('Deleting KPI record:', id)
    const result = await api.database.deleteKPIRecord(id)
    if (result.success) {
      console.log('KPI record deleted successfully')
    } else {
      console.error('Failed to delete KPI record:', result.error)
    }
    return result
  },

  // Area Metric operations
  async createAreaMetric(data: CreateAreaMetricRequest) {
    const api = getElectronAPI()
    console.log('Creating area metric:', data)
    const result = await api.database.createAreaMetric(data)
    if (result.success) {
      console.log('Area metric created successfully:', result.data)
    } else {
      console.error('Failed to create area metric:', result.error)
    }
    return result
  },

  async getAreaMetrics(areaId: string) {
    const api = getElectronAPI()
    console.log('Fetching area metrics for area:', areaId)
    const result = await api.database.getAreaMetrics(areaId)
    if (result.success) {
      console.log(`Fetched ${result.data?.length || 0} area metrics`)
    } else {
      console.error('Failed to fetch area metrics:', result.error)
    }
    return result
  },

  async updateAreaMetric(data: UpdateAreaMetricRequest) {
    const api = getElectronAPI()
    console.log('Updating area metric:', data.id)
    const result = await api.database.updateAreaMetric(data)
    if (result.success) {
      console.log('Area metric updated successfully')
    } else {
      console.error('Failed to update area metric:', result.error)
    }
    return result
  },

  async deleteAreaMetric(id: string) {
    const api = getElectronAPI()
    console.log('Deleting area metric:', id)
    const result = await api.database.deleteAreaMetric(id)
    if (result.success) {
      console.log('Area metric deleted successfully')
    } else {
      console.error('Failed to delete area metric:', result.error)
    }
    return result
  },

  // Area Metric Record operations
  async createAreaMetricRecord(data: CreateAreaMetricRecordRequest) {
    const api = getElectronAPI()
    console.log('Creating area metric record:', data)
    const result = await api.database.createAreaMetricRecord(data)
    if (result.success) {
      console.log('Area metric record created successfully:', result.data)
    } else {
      console.error('Failed to create area metric record:', result.error)
    }
    return result
  },

  async getAreaMetricRecords(metricId: string, limit?: number) {
    const api = getElectronAPI()
    console.log('Fetching area metric records for metric:', metricId)
    const result = await api.database.getAreaMetricRecords(metricId, limit)
    if (result.success) {
      console.log(`Fetched ${result.data?.length || 0} area metric records`)
    } else {
      console.error('Failed to fetch area metric records:', result.error)
    }
    return result
  },

  async updateAreaMetricRecord(data: UpdateAreaMetricRecordRequest) {
    const api = getElectronAPI()
    console.log('Updating area metric record:', data.id)
    const result = await api.database.updateAreaMetricRecord(data)
    if (result.success) {
      console.log('Area metric record updated successfully')
    } else {
      console.error('Failed to update area metric record:', result.error)
    }
    return result
  },

  async deleteAreaMetricRecord(id: string) {
    const api = getElectronAPI()
    console.log('Deleting area metric record:', id)
    const result = await api.database.deleteAreaMetricRecord(id)
    if (result.success) {
      console.log('Area metric record deleted successfully')
    } else {
      console.error('Failed to delete area metric record:', result.error)
    }
    return result
  },

  // Deliverable operations
  async createDeliverable(data: any) {
    const api = getElectronAPI()
    console.log('Creating deliverable:', data)
    const result = await api.database.createDeliverable(data)
    if (result.success) {
      console.log('Deliverable created successfully:', result.data)
    } else {
      console.error('Failed to create deliverable:', result.error)
    }
    return result
  },

  async getProjectDeliverables(projectId: string) {
    const api = getElectronAPI()
    console.log('Fetching project deliverables for project:', projectId)
    const result = await api.database.getProjectDeliverables(projectId)
    if (result.success) {
      console.log(`Fetched ${result.data?.length || 0} project deliverables`)
    } else {
      console.error('Failed to fetch project deliverables:', result.error)
    }
    return result
  },

  async updateDeliverable(data: any) {
    const api = getElectronAPI()
    console.log('Updating deliverable:', data)
    const result = await api.database.updateDeliverable(data)
    if (result.success) {
      console.log('Deliverable updated successfully:', result.data)
    } else {
      console.error('Failed to update deliverable:', result.error)
    }
    return result
  },

  async deleteDeliverable(id: string) {
    const api = getElectronAPI()
    console.log('Deleting deliverable:', id)
    const result = await api.database.deleteDeliverable(id)
    if (result.success) {
      console.log('Deliverable deleted successfully')
    } else {
      console.error('Failed to delete deliverable:', result.error)
    }
    return result
  },

  // Resource linking operations
  async getProjectResources(projectId: string) {
    const api = getElectronAPI()
    console.log('Fetching project resources for project:', projectId)
    const result = await api.database.getProjectResources(projectId)
    if (result.success) {
      console.log(`Fetched ${result.data?.length || 0} project resources`)

    } else {
      console.error('Failed to fetch project resources:', result.error)
    }
    return result
  },

  async linkResourceToProject(data: LinkResourceToProjectRequest) {
    const api = getElectronAPI()
    console.log('Linking resource to project:', data)
    const result = await api.database.linkResourceToProject(data)
    if (result.success) {
      console.log('Resource linked to project successfully')
    } else {
      console.error('Failed to link resource to project:', result.error)
    }
    return result
  },

  async unlinkResourceFromProject(resourceId: string, projectId: string) {
    const api = getElectronAPI()
    console.log('Unlinking resource from project:', { resourceId, projectId })
    const result = await api.database.unlinkResourceFromProject(resourceId, projectId)
    if (result.success) {
      console.log('Resource unlinked from project successfully')
    } else {
      console.error('Failed to unlink resource from project:', result.error)
    }
    return result
  },

  // Area resource operations
  async getAreaResources(areaId: string) {
    const api = getElectronAPI()
    console.log('Fetching area resources for area:', areaId)
    const result = await api.database.getAreaResources(areaId)
    if (result.success) {
      console.log(`Fetched ${result.data?.length || 0} area resources`)
    } else {
      console.error('Failed to fetch area resources:', result.error)
    }
    return result
  },

  async unlinkResourceFromArea(resourceId: string, areaId: string) {
    const api = getElectronAPI()
    console.log('Unlinking resource from area:', { resourceId, areaId })
    const result = await api.database.unlinkResourceFromArea(resourceId, areaId)
    if (result.success) {
      console.log('Resource unlinked from area successfully')
    } else {
      console.error('Failed to unlink resource from area:', result.error)
    }
    return result
  },

  // Document Link operations
  async createDocumentLink(data: any) {
    const api = getElectronAPI()
    console.log('Creating document link:', data)
    const result = await api.database.createDocumentLink(data)
    if (result.success) {
      console.log('Document link created successfully')
    } else {
      console.error('Failed to create document link:', result.error)
    }
    return result
  },

  async getDocumentLinks(docPath: string) {
    const api = getElectronAPI()
    console.log('Getting document links for:', docPath)
    const result = await api.database.getDocumentLinks(docPath)
    if (result.success) {
      console.log('Document links retrieved successfully')
    } else {
      console.error('Failed to get document links:', result.error)
    }
    return result
  },

  async getBacklinks(docPath: string) {
    const api = getElectronAPI()
    const result = await api.database.getBacklinks(docPath)
    if (result.success) {
      console.log('Backlinks retrieved successfully:', result.data?.length)
    } else {
      console.error('Failed to get backlinks:', result.error)
    }
    return result
  },

  async getOutlinks(docPath: string) {
    const api = getElectronAPI()
    const result = await api.database.getOutlinks(docPath)
    if (result.success) {
      console.log('Outlinks retrieved successfully:', result.data?.length)
    } else {
      console.error('Failed to get outlinks:', result.error)
    }
    return result
  },

  async getLinkStatistics(docPath: string) {
    const api = getElectronAPI()
    const result = await api.database.getLinkStatistics(docPath)
    if (result.success) {
      console.log('Link statistics retrieved successfully')
    } else {
      console.error('Failed to get link statistics:', result.error)
    }
    return result
  },

  async replaceDocumentLinks(data: any) {
    const api = getElectronAPI()
    console.log('Replacing document links for:', data.sourceDocPath)
    const result = await api.database.replaceDocumentLinks(data)
    if (result.success) {
      console.log('Document links replaced successfully:', result.data?.length)
    } else {
      console.error('Failed to replace document links:', result.error)
    }
    return result
  },

  // Habit operations
  habits: {
    async getByArea(areaId: string) {
      const api = getElectronAPI()
      console.log('Getting habits for area:', areaId)
      const result = await api.database.getHabitsByArea(areaId)
      if (result.success) {
        console.log('Successfully retrieved habits:', result.data?.length || 0)
      } else {
        console.error('Failed to get habits:', result.error)
      }
      return result
    }
  }
}

// File System API wrapper
export const fileSystemApi = {
  async initialize() {
    const api = getElectronAPI()
    console.log('Initializing file system...')
    const result = await api.fileSystem.initialize()
    if (result.success) {
      console.log('File system initialized successfully')
    } else {
      console.error('File system initialization failed:', result.error)
    }
    return result
  },

  async reinitialize(workspaceDirectory: string) {
    const api = getElectronAPI()
    console.log('Re-initializing file system with workspace:', workspaceDirectory)
    try {
      const result = await api.fileSystem.reinitialize(workspaceDirectory)
      console.log('Reinitialize result:', result)
      if (result.success) {
        console.log('File system re-initialized successfully')
        console.log('New config:', result.data)
      } else {
        console.error('Failed to re-initialize file system:', result.error)
      }
      return result
    } catch (error) {
      console.error('Exception during reinitialize:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  },

  async readFile(data: ReadFileRequest) {
    const api = getElectronAPI()
    console.log('Reading file:', data.path)
    const result = await api.fileSystem.readFile(data)
    if (result.success) {
      console.log('File read successfully')
    } else {
      console.error('Failed to read file:', result.error)
    }
    return result
  },

  async writeFile(data: WriteFileRequest) {
    const api = getElectronAPI()
    console.log('Writing file:', data.path)
    const result = await api.fileSystem.writeFile(data)
    if (result.success) {
      console.log('File written successfully')
    } else {
      console.error('Failed to write file:', result.error)
    }
    return result
  },

  async deleteFile(path: string) {
    const api = getElectronAPI()
    console.log('Deleting file:', path)
    const result = await api.fileSystem.deleteFile(path)
    if (result.success) {
      console.log('File deleted successfully')
    } else {
      console.error('Failed to delete file:', result.error)
    }
    return result
  },

  async moveFile(data: MoveFileRequest) {
    const api = getElectronAPI()
    console.log('Moving file:', data.sourcePath, '->', data.targetPath)
    const result = await api.fileSystem.moveFile(data)
    if (result.success) {
      console.log('File moved successfully')
    } else {
      console.error('Failed to move file:', result.error)
    }
    return result
  },

  async copyFile(data: CopyFileRequest) {
    const api = getElectronAPI()
    console.log('Copying file:', data.sourcePath, '->', data.targetPath)
    const result = await api.fileSystem.copyFile(data)
    if (result.success) {
      console.log('File copied successfully')
    } else {
      console.error('Failed to copy file:', result.error)
    }
    return result
  },

  async fileExists(path: string) {
    const api = getElectronAPI()
    const result = await api.fileSystem.fileExists(path)
    return result
  },

  async getFileInfo(path: string) {
    const api = getElectronAPI()
    console.log('Getting file info:', path)
    const result = await api.fileSystem.getFileInfo(path)
    if (result.success) {
      console.log('File info retrieved successfully')
    } else {
      console.error('Failed to get file info:', result.error)
    }
    return result
  },

  async listDirectory(path: string) {
    const api = getElectronAPI()
    console.log('Listing directory:', path)
    const result = await api.fileSystem.listDirectory(path)
    if (result.success) {
      console.log(`Directory listed successfully, found ${result.data?.length || 0} items`)
    } else {
      console.error('Failed to list directory:', result.error)
    }
    return result
  },

  async createDirectory(path: string) {
    const api = getElectronAPI()
    console.log('Creating directory:', path)
    const result = await api.fileSystem.createDirectory(path)
    if (result.success) {
      console.log('Directory created successfully')
    } else {
      console.error('Failed to create directory:', result.error)
    }
    return result
  },

  async deleteDirectory(path: string) {
    const api = getElectronAPI()
    console.log('Deleting directory:', path)
    const result = await api.fileSystem.deleteDirectory(path)
    if (result.success) {
      console.log('Directory deleted successfully')
    } else {
      console.error('Failed to delete directory:', result.error)
    }
    return result
  },

  async rename(oldPath: string, newPath: string) {
    const api = getElectronAPI()
    console.log('Renaming:', oldPath, 'to', newPath)
    const result = await api.fileSystem.rename(oldPath, newPath)
    if (result.success) {
      console.log('Rename successful')
    } else {
      console.error('Failed to rename:', result.error)
    }
    return result
  },

  async watchDirectory(path: string) {
    const api = getElectronAPI()
    console.log('Watching directory:', path)
    const result = await api.fileSystem.watchDirectory(path)
    if (result.success) {
      console.log('Directory watch started successfully')
    } else {
      console.error('Failed to watch directory:', result.error)
    }
    return result
  },

  async unwatchDirectory(path: string) {
    const api = getElectronAPI()
    console.log('Unwatching directory:', path)
    const result = await api.fileSystem.unwatchDirectory(path)
    if (result.success) {
      console.log('Directory watch stopped successfully')
    } else {
      console.error('Failed to unwatch directory:', result.error)
    }
    return result
  }
}

// Application API wrapper
export const appApi = {
  async getVersion() {
    const api = getElectronAPI()
    return await api.app.getVersion()
  },

  async getPath(name: string) {
    const api = getElectronAPI()
    return await api.app.getPath(name)
  },

  async showMessageBox(options: any) {
    const api = getElectronAPI()
    return await api.app.showMessageBox(options)
  },

  async showErrorBox(title: string, content: string) {
    const api = getElectronAPI()
    return await api.app.showErrorBox(title, content)
  },

  async showOpenDialog(options: any) {
    const api = getElectronAPI()
    return await api.app.showOpenDialog(options)
  },

  async showSaveDialog(options: any) {
    const api = getElectronAPI()
    return await api.app.showSaveDialog(options)
  }
}

// Window API wrapper
export const windowApi = {
  async minimize() {
    const api = getElectronAPI()
    return await api.window.minimize()
  },

  async maximize() {
    const api = getElectronAPI()
    return await api.window.maximize()
  },

  async close() {
    const api = getElectronAPI()
    return await api.window.close()
  },

  async toggleDevTools() {
    const api = getElectronAPI()
    return await api.window.toggleDevTools()
  }
}

// Event API wrapper
export const eventApi = {
  onFileSystemEvent(callback: (event: any) => void) {
    const api = getElectronAPI()
    return api.onFileSystemEvent(callback)
  },

  onDatabaseEvent(callback: (event: any) => void) {
    const api = getElectronAPI()
    return api.onDatabaseEvent(callback)
  }
}

// Combined API export
export const electronApi = {
  database: databaseApi,
  fileSystem: fileSystemApi,
  app: appApi,
  window: windowApi,
  events: eventApi
}

// Default export
export default electronApi
