import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { PageHeader, PageHeaderActions } from '../components/shared'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs'
import { useLanguage } from '../contexts/LanguageContext'
import { useUIStore } from '../store/uiStore'
import { useProjectStore } from '../store/projectStore'
import { useAreaStore } from '../store/areaStore'
import { databaseApi } from '../lib/api'
import ArchiveItem, {
  type ArchiveItem as ArchiveItemType
} from '../components/features/ArchiveItem'

export function ArchivePage() {
  const [items, setItems] = useState<ArchiveItemType[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<string>('all')
  const [filterReason, setFilterReason] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('archivedAt')
  const [isLoading, setIsLoading] = useState(true)
  const { t } = useLanguage()
  const { addNotification } = useUIStore()
  const { fetchProjects } = useProjectStore()
  const { fetchAreas } = useAreaStore()
  const navigate = useNavigate()

  // 获取归档数据
  useEffect(() => {
    const fetchArchivedItems = async () => {
      setIsLoading(true)
      try {
        const [projectsResult, areasResult] = await Promise.all([
          databaseApi.getArchivedProjects(),
          databaseApi.getArchivedAreas()
        ])

        const archivedItems: ArchiveItemType[] = []

        // 处理归档项目
        if (projectsResult.success && projectsResult.data) {
          const projectItems: ArchiveItemType[] = projectsResult.data.map((project: any) => ({
            id: project.id,
            title: project.name,
            description: project.description || '',
            type: 'project' as const,
            originalStatus: project.status || 'Unknown',
            archivedAt: project.updatedAt,
            archivedReason: 'manual' as const, // 默认为手动归档，后续可扩展
            tags: [], // 项目标签功能待实现
            metadata: {
              originalId: project.id,
              lastActivity: project.updatedAt
            }
          }))
          archivedItems.push(...projectItems)
        }

        // 处理归档领域
        if (areasResult.success && areasResult.data) {
          const areaItems: ArchiveItemType[] = areasResult.data.map((area: any) => ({
            id: area.id,
            title: area.name,
            description: area.description || '',
            type: 'area' as const,
            originalStatus: area.status || 'Unknown',
            archivedAt: area.updatedAt,
            archivedReason: 'manual' as const, // 默认为手动归档，后续可扩展
            tags: [], // 领域标签功能待实现
            metadata: {
              originalId: area.id,
              lastActivity: area.updatedAt
            }
          }))
          archivedItems.push(...areaItems)
        }

        // 按归档时间排序
        archivedItems.sort((a, b) => new Date(b.archivedAt).getTime() - new Date(a.archivedAt).getTime())

        setItems(archivedItems)
      } catch (error) {
        console.error('Failed to fetch archived items:', error)
        addNotification({
          type: 'error',
          title: 'Failed to load archived items',
          message: error instanceof Error ? error.message : 'Unknown error occurred'
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchArchivedItems()
  }, [])

  const handleRestore = async (item: ArchiveItemType) => {
    try {
      let result
      if (item.type === 'project') {
        result = await databaseApi.restoreProject(item.id)
      } else if (item.type === 'area') {
        result = await databaseApi.restoreArea(item.id)
      } else {
        console.warn('Resource restoration not implemented yet')
        return
      }

      if (result.success) {
        // 从列表中移除已恢复的项目
        setItems((prev) => prev.filter((i) => i.id !== item.id))

        // 刷新对应模块的数据，确保恢复的项目显示在列表中
        if (item.type === 'project') {
          fetchProjects()
        } else if (item.type === 'area') {
          fetchAreas()
        }

        addNotification({
          type: 'success',
          title: t('pages.archive.restore'),
          message: `"${item.title}" ${t('pages.archive.restore')} successfully`
        })
        console.log(`${item.type} restored successfully:`, item.title)
      } else {
        addNotification({
          type: 'error',
          title: `Failed to ${t('pages.archive.restore')}`,
          message: result.error || 'Unknown error occurred'
        })
        console.error(`Failed to restore ${item.type}:`, result.error)
      }
    } catch (error) {
      console.error('Error restoring item:', error)
    }
  }

  const handleDelete = async (itemId: string) => {
    try {
      const item = items.find(i => i.id === itemId)
      if (!item) return

      let result
      if (item.type === 'project') {
        result = await databaseApi.deleteProject(itemId)
      } else if (item.type === 'area') {
        result = await databaseApi.deleteArea(itemId)
      } else {
        console.warn('Resource deletion not implemented yet')
        return
      }

      if (result.success) {
        // 从列表中移除已删除的项目
        setItems((prev) => prev.filter((item) => item.id !== itemId))
        addNotification({
          type: 'success',
          title: t('pages.archive.deletePermanently'),
          message: `"${item.title}" has been deleted permanently`
        })
        console.log(`${item.type} deleted permanently:`, item.title)
      } else {
        addNotification({
          type: 'error',
          title: `Failed to delete ${item.type}`,
          message: result.error || 'Unknown error occurred'
        })
        console.error(`Failed to delete ${item.type}:`, result.error)
      }
    } catch (error) {
      console.error('Error deleting item:', error)
    }
  }

  const handleViewDetails = (item: ArchiveItemType) => {
    // 导航到对应的详情页面，并传递archived状态
    if (item.type === 'project') {
      navigate(`/projects/${item.id}`, { state: { archived: true } })
    } else if (item.type === 'area') {
      navigate(`/areas/${item.id}`, { state: { archived: true } })
    }
  }

  const handleView = (item: ArchiveItemType) => {
    console.log('View item:', item)
    // In real implementation, this would open a detailed view
  }

  // Filter and sort items
  const filteredItems = items
    .filter((item) => {
      const matchesSearch =
        !searchQuery ||
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))

      const matchesType = filterType === 'all' || item.type === filterType
      const matchesReason = filterReason === 'all' || item.archivedReason === filterReason

      return matchesSearch && matchesType && matchesReason
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'title':
          return a.title.localeCompare(b.title)
        case 'type':
          return a.type.localeCompare(b.type)
        case 'archivedAt':
        default:
          return new Date(b.archivedAt).getTime() - new Date(a.archivedAt).getTime()
      }
    })

  const getItemCounts = () => {
    const projects = items.filter((item) => item.type === 'project').length
    const areas = items.filter((item) => item.type === 'area').length
    const resources = items.filter((item) => item.type === 'resource').length
    return { projects, areas, resources, total: items.length }
  }

  const counts = getItemCounts()

  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader
        title={t('pages.archive.title')}
        description={t('pages.archive.description')}
        badge={{
          text: `${counts.total} ${t('enums.types.project').toLowerCase()}`,
          variant: 'outline'
        }}
        actions={
          <PageHeaderActions.Settings onClick={() => console.log('Archive settings')}>
            {t('pages.archive.settings')}
          </PageHeaderActions.Settings>
        }
        className="border-l-4 border-archive pl-6"
      />

      {/* Archive Categories */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge className="para-project">P</Badge>
              {t('pages.archive.archivedProjects')}
            </CardTitle>
            <CardDescription>{t('pages.archive.completedOrCancelled')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{counts.projects}</div>
            <p className="text-xs text-muted-foreground">
              {counts.projects > 0
                ? t('pages.archive.lastArchivedRecently')
                : t('pages.archive.noArchivedItems')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge className="para-area">A</Badge>
              {t('pages.archive.archivedAreas')}
            </CardTitle>
            <CardDescription>{t('pages.archive.noLongerMaintained')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{counts.areas}</div>
            <p className="text-xs text-muted-foreground">
              {counts.areas > 0
                ? t('pages.archive.lastArchivedRecently')
                : t('pages.archive.noArchivedItems')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge className="para-resource">R</Badge>
              {t('pages.archive.archivedResources')}
            </CardTitle>
            <CardDescription>{t('pages.archive.noLongerRelevant')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{counts.resources}</div>
            <p className="text-xs text-muted-foreground">
              {counts.resources > 0
                ? t('pages.archive.lastArchivedRecently')
                : t('pages.archive.noArchivedItems')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder={t('pages.archive.searchPlaceholder')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>

        <div className="flex gap-2">
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('pages.inbox.filters.type.allTypes')}</SelectItem>
              <SelectItem value="project">📋 {t('enums.types.project')}</SelectItem>
              <SelectItem value="area">🏠 {t('enums.types.area')}</SelectItem>
              <SelectItem value="resource">📄 {t('enums.types.resource')}</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filterReason} onValueChange={setFilterReason}>
            <SelectTrigger className="w-36">
              <SelectValue placeholder="Reason" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Reasons</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="outdated">Outdated</SelectItem>
              <SelectItem value="manual">Manual</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Sort" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="archivedAt">Date Archived</SelectItem>
              <SelectItem value="title">Title</SelectItem>
              <SelectItem value="type">Type</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Archive Items */}
      <Tabs defaultValue="items" className="space-y-4">
        <TabsList>
          <TabsTrigger value="items">Archived Items</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="items" className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex items-center gap-2 text-muted-foreground">
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                <span>Loading archived items...</span>
              </div>
            </div>
          ) : filteredItems.length === 0 ? (
            <div className="text-center py-12">
              {items.length === 0 ? (
                <div className="text-muted-foreground">
                  <div className="text-4xl mb-2">📦</div>
                  <p className="text-sm">No archived items yet</p>
                  <p className="text-xs mt-1">Items will appear here when archived</p>
                </div>
              ) : (
                <div className="text-muted-foreground">
                  <div className="text-4xl mb-2">🔍</div>
                  <p className="text-sm">No items match your filters</p>
                  <p className="text-xs mt-1">Try adjusting your search or filters</p>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {filteredItems.map((item) => (
                <ArchiveItem
                  key={item.id}
                  item={item}
                  onRestore={handleRestore}
                  onDelete={handleDelete}
                  onView={handleViewDetails}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>{t('pages.archive.management')}</CardTitle>
              <CardDescription>{t('pages.archive.managementDescription')}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">{t('pages.archive.autoArchiveProjects')}</h4>
                  <p className="text-sm text-muted-foreground">
                    {t('pages.archive.autoArchiveDescription')}
                  </p>
                </div>
                <Badge variant="secondary">{t('pages.archive.enabled')}</Badge>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">{t('pages.archive.cleanupOldArchives')}</h4>
                  <p className="text-sm text-muted-foreground">
                    {t('pages.archive.cleanupDescription')}
                  </p>
                </div>
                <Badge variant="outline">{t('pages.archive.disabled')}</Badge>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">{t('pages.archive.archiveNotifications')}</h4>
                  <p className="text-sm text-muted-foreground">
                    {t('pages.archive.notificationsDescription')}
                  </p>
                </div>
                <Badge variant="secondary">{t('pages.archive.enabled')}</Badge>
              </div>

              <div className="pt-4 border-t">
                <Button variant="outline" className="w-full">
                  {t('pages.archive.exportArchiveData')}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ArchivePage
