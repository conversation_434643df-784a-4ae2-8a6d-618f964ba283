import { Outlet, useLocation } from 'react-router-dom'
import { useState, useMemo } from 'react'
import { <PERSON>Header, PageHeaderActions, EmptyStates } from '../components/shared'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../components/ui/select'
import { Badge } from '../components/ui/badge'
import ProjectCard from '../components/features/ProjectCard'
import KanbanColumn from '../components/features/KanbanColumn'
import CreateProjectDialog from '../components/features/CreateProjectDialog'
import { useConfirmDialog } from '../components/shared/ConfirmDialog'
import { useProjectStore } from '../store/projectStore'
import { useAreaStore } from '../store/areaStore'
import { useTaskStore } from '../store/taskStore'
import { useLanguage } from '../contexts/LanguageContext'
import { databaseApi } from '../lib/api'
import { useUIStore } from '../store/uiStore'
import { cn } from '../lib/utils'
import type { Project } from '../../../shared/types'

export function ProjectsPage() {
  const location = useLocation()
  const isDetailView = location.pathname !== '/projects'

  const { projects, addProject, updateProject, deleteProject, archiveProject } = useProjectStore()
  const { areas } = useAreaStore()
  const { tasks } = useTaskStore()
  const { t } = useLanguage()
  const { addNotification } = useUIStore()
  const { confirm, ConfirmDialog: ConfirmDialogComponent } = useConfirmDialog()

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingProject, setEditingProject] = useState<Project | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'kanban'>('grid')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [areaFilter, setAreaFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'name' | 'deadline' | 'progress' | 'updated'>('updated')

  // Filter and sort projects
  const filteredProjects = useMemo(() => {
    let filtered = projects.filter((project) => !project.archived)

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (project) =>
          project.name.toLowerCase().includes(query) ||
          project.description?.toLowerCase().includes(query) ||
          project.goal?.toLowerCase().includes(query)
      )
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter((project) => project.status === statusFilter)
    }

    // Area filter
    if (areaFilter !== 'all') {
      filtered = filtered.filter((project) => project.areaId === areaFilter)
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'deadline':
          if (!a.deadline && !b.deadline) return 0
          if (!a.deadline) return 1
          if (!b.deadline) return -1
          return new Date(a.deadline).getTime() - new Date(b.deadline).getTime()
        case 'progress':
          return b.progress - a.progress
        case 'updated':
        default:
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      }
    })

    return filtered
  }, [projects, searchQuery, statusFilter, areaFilter, sortBy, tasks])

  // Group projects by status for kanban view (exclude completed projects as they get archived)
  const kanbanData = useMemo(() => {
    const statuses = [
      { key: 'Not Started', label: t('pages.projects.filters.status.notStarted') },
      { key: 'In Progress', label: t('pages.projects.filters.status.inProgress') },
      { key: 'At Risk', label: t('pages.projects.filters.status.atRisk') },
      { key: 'Paused', label: t('pages.projects.filters.status.paused') }
    ]

    return statuses.map((status) => ({
      ...status,
      projects: filteredProjects.filter((project) => project.status === status.key)
    }))
  }, [filteredProjects, t])

  const handleCreateProject = async (
    projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>
  ) => {
    try {
      const result = await databaseApi.createProject({
        name: projectData.name,
        description: projectData.description || undefined,
        goal: projectData.goal || undefined
      })

      if (result.success) {
        // Add project to the store with the actual database data
        const newProject: Project = {
          id: result.data.id,
          name: result.data.name,
          description: result.data.description,
          goal: result.data.goal,
          deliverable: projectData.deliverable,
          status: projectData.status,
          progress: projectData.progress,
          startDate: projectData.startDate,
          deadline: projectData.deadline,
          areaId: projectData.areaId,
          archived: result.data.archived || false,
          createdAt: new Date(result.data.createdAt),
          updatedAt: new Date(result.data.updatedAt)
        }

        addProject(newProject)

        addNotification({
          type: 'success',
          title: '项目创建成功',
          message: `项目 "${projectData.name}" 已创建`
        })
      } else {
        throw new Error(result.error || '创建项目失败')
      }
    } catch (error) {
      console.error('Failed to create project:', error)
      addNotification({
        type: 'error',
        title: '创建项目失败',
        message: error instanceof Error ? error.message : '创建项目时发生未知错误'
      })
    }
  }

  const handleEditProject = async (
    projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>
  ) => {
    if (editingProject) {
      updateProject(editingProject.id, {
        ...projectData,
        updatedAt: new Date()
      })
      setEditingProject(null)
    }
  }

  const handleDeleteProject = (project: Project) => {
    confirm({
      title: '删除项目',
      description: `确定要删除"${project.name}"吗？此操作无法撤销。`,
      variant: 'destructive',
      confirmText: '删除',
      cancelText: '取消',
      onConfirm: () => {
        deleteProject(project.id)
      }
    })
  }

  const handleArchiveProject = (project: Project) => {
    confirm({
      title: '归档项目',
      description: `归档"${project.name}"？您可以稍后从归档中恢复。`,
      variant: 'warning',
      confirmText: '归档',
      cancelText: '取消',
      onConfirm: async () => {
        await archiveProject(project.id)
      }
    })
  }

  const statusOptions = [
    { value: 'all', label: t('pages.projects.allStatus') },
    { value: 'Not Started', label: t('pages.projects.filters.status.notStarted') },
    { value: 'In Progress', label: t('pages.projects.filters.status.inProgress') },
    { value: 'At Risk', label: t('pages.projects.filters.status.atRisk') },
    { value: 'Paused', label: t('pages.projects.filters.status.paused') },
    { value: 'Completed', label: t('pages.projects.filters.status.completed') }
  ]

  const sortOptions = [
    { value: 'updated', label: t('pages.projects.filters.sort.updated') },
    { value: 'name', label: t('pages.projects.filters.sort.name') },
    { value: 'deadline', label: t('pages.projects.filters.sort.deadline') },
    { value: 'progress', label: t('pages.projects.filters.sort.progress') }
  ]

  // Handle detail view routing
  if (isDetailView) {
    return <Outlet />
  }

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      <div className="container mx-auto p-6 flex flex-col h-full">
        <div className="flex-shrink-0">
          <PageHeader
            title={t('pages.projects.title')}
            description={t('pages.projects.description')}
            badge={{ text: 'P.A.R.A.', variant: 'secondary' }}
            actions={
              <PageHeaderActions.Create onClick={() => setIsCreateDialogOpen(true)}>
                {t('pages.projects.newProject')}
              </PageHeaderActions.Create>
            }
            className="border-l-4 border-project pl-6"
          />
        </div>

        {projects.filter((p) => !p.archived).length === 0 ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="max-w-md">
              <EmptyStates.Projects onCreate={() => setIsCreateDialogOpen(true)} />
            </div>
          </div>
        ) : (
          <div className="flex-1 flex flex-col min-h-0">
            {/* Filters and Controls */}
            <div className="flex-shrink-0 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between py-4">
              <div className="flex flex-col sm:flex-row gap-3 flex-1">
                <Input
                  placeholder={t('pages.projects.searchPlaceholder')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="sm:max-w-xs"
                />

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="sm:w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={areaFilter} onValueChange={setAreaFilter}>
                  <SelectTrigger className="sm:w-40">
                    <SelectValue placeholder={t('pages.projects.allAreas')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('pages.projects.allAreas')}</SelectItem>
                    {areas
                      .filter((area) => !area.archived)
                      .map((area) => (
                        <SelectItem key={area.id} value={area.id}>
                          {area.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                  <SelectTrigger className="sm:w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {sortOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center gap-2">
                <div className="flex items-center border rounded-lg p-1">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="h-7 px-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                      />
                    </svg>
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="h-7 px-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 6h16M4 10h16M4 14h16M4 18h16"
                      />
                    </svg>
                  </Button>
                  <Button
                    variant={viewMode === 'kanban' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('kanban')}
                    className="h-7 px-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h2m0-14h2.5a2 2 0 012 2v6a2 2 0 01-2 2H11m-2-8v8m8-8V7a2 2 0 00-2-2h-2.5"
                      />
                    </svg>
                  </Button>
                </div>

                <Badge variant="outline" className="text-xs">
                  {filteredProjects.length} {t('nav.projects').toLowerCase()}
                </Badge>
              </div>
            </div>

            {/* Projects Views - Fixed height relative to window */}
            <div className="h-[calc(100vh-280px)] overflow-hidden">
              {filteredProjects.length === 0 ? (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  <div className="text-center">
                    <div className="text-4xl mb-2">🔍</div>
                    <p className="text-sm">{t('components.emptyStates.noItemsFound')}</p>
                    <p className="text-xs mt-1">
                      {t('components.emptyStates.tryAdjustingFilters')}
                    </p>
                  </div>
                </div>
              ) : viewMode === 'kanban' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 h-full">
                  {kanbanData.map((column) => (
                    <KanbanColumn
                      key={column.key}
                      title={column.label}
                      status={column.key}
                      projects={column.projects}
                      onEdit={setEditingProject}
                      onDelete={handleDeleteProject}
                      onArchive={handleArchiveProject}
                    />
                  ))}
                </div>
              ) : (
                <div className="h-full overflow-y-auto scrollbar-hide pr-2">
                  <div
                    className={cn(
                      'grid gap-6 pb-6',
                      viewMode === 'grid'
                        ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                        : 'grid-cols-1'
                    )}
                  >
                    {filteredProjects.map((project) => (
                      <ProjectCard
                        key={project.id}
                        project={project}
                        onEdit={setEditingProject}
                        onDelete={handleDeleteProject}
                        onArchive={handleArchiveProject}
                        className={viewMode === 'list' ? 'max-w-none' : ''}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Create/Edit Project Dialog */}
        <CreateProjectDialog
          isOpen={isCreateDialogOpen || !!editingProject}
          onClose={() => {
            setIsCreateDialogOpen(false)
            setEditingProject(null)
          }}
          onSubmit={editingProject ? handleEditProject : handleCreateProject}
          initialData={editingProject || undefined}
        />

        {/* Confirm Dialog */}
        <ConfirmDialogComponent />
      </div>
    </div>
  )
}

export default ProjectsPage
